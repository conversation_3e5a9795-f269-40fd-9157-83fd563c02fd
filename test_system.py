#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试宿舍分配系统
验证所有功能是否正常工作
"""

import os
import csv

def test_file_existence():
    """测试必需文件是否存在"""
    print("=== 测试文件存在性 ===")
    
    required_files = [
        "2025级法律学系新生宿舍组队.xlsx",
        "需排除人员名单.csv",
        "已匹配的宿舍.csv",
        "宿舍情况.csv"
    ]

    # 检查是否存在主学生名单文件
    master_file = "法学院人员名单.csv"
    if os.path.exists(master_file):
        print(f"✓ {master_file}")
    else:
        print(f"? {master_file} - 不存在（需要运行extract_data.py生成）")
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n警告: 缺失 {len(missing_files)} 个必需文件")
        return False
    else:
        print("\n所有必需文件都存在")
        return True

def test_extracted_files():
    """测试提取的文件"""
    print("\n=== 测试提取的文件 ===")
    
    extracted_files = [
        "法学院人员名单_法学女生.csv",
        "法学院人员名单_法学男生.csv",
        "法学院人员名单_法外女生.csv",
        "法学院人员名单_法外男生.csv",
        "需排除人员名单_utf8.csv",
        "已匹配的宿舍_utf8.csv",
        "宿舍情况_utf8.csv"
    ]
    
    for file in extracted_files:
        if os.path.exists(file):
            try:
                with open(file, 'r', encoding='utf-8-sig', newline='') as f:
                    reader = csv.DictReader(f)
                    rows = list(reader)
                    print(f"✓ {file} - {len(rows)} 行")
            except Exception as e:
                print(f"✗ {file} - 读取错误: {e}")
        else:
            print(f"? {file} - 不存在（需要运行extract_data.py）")

def test_data_integrity():
    """测试数据完整性"""
    print("\n=== 测试数据完整性 ===")
    
    try:
        # 测试学生数据（从主文件）
        master_file = "法学院人员名单.csv"
        if os.path.exists(master_file):
            with open(master_file, 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                students = list(reader)
                total_students = len(students)
                male_students = len([s for s in students if s.get('性别') == '男'])
                female_students = len([s for s in students if s.get('性别') == '女'])
                law_students = len([s for s in students if s.get('专业') == '法学'])
                non_law_students = len([s for s in students if s.get('专业') == '法外'])

                print(f"学生总数: {total_students}")
                print(f"  男生: {male_students}")
                print(f"  女生: {female_students}")
                print(f"  法学专业: {law_students}")
                print(f"  法外专业: {non_law_students}")
        else:
            print("法学院人员名单.csv 不存在，请先运行 extract_data.py")
            total_students = 0
        
        # 测试宿舍数据
        if os.path.exists("宿舍情况_utf8.csv"):
            with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                beds = list(reader)
                male_beds = len([b for b in beds if b.get('性别') == '男'])
                female_beds = len([b for b in beds if b.get('性别') == '女'])
                print(f"床位总数: {len(beds)} (男: {male_beds}, 女: {female_beds})")
        
        # 测试排除名单
        if os.path.exists("需排除人员名单_utf8.csv"):
            with open("需排除人员名单_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                exclude_list = list(reader)
                print(f"需排除学生: {len(exclude_list)}")
        
        # 测试已匹配名单
        if os.path.exists("已匹配的宿舍_utf8.csv"):
            with open("已匹配的宿舍_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                matched_list = list(reader)
                print(f"已匹配学生: {len(matched_list)}")
        
        return True
        
    except Exception as e:
        print(f"数据完整性测试失败: {e}")
        return False

def test_allocation_logic():
    """测试分配逻辑（简单验证）"""
    print("\n=== 测试分配逻辑 ===")
    
    try:
        # 这里可以添加简单的逻辑测试
        print("分配逻辑测试需要运行完整程序")
        print("请运行: python final_dormitory_allocation.py")
        return True
        
    except Exception as e:
        print(f"分配逻辑测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n=== 生成测试报告 ===")
    
    report = f"""宿舍分配系统测试报告
生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

测试项目:
1. 文件存在性检查
2. 提取文件验证
3. 数据完整性检查
4. 分配逻辑验证

建议运行步骤:
1. 确保所有输入文件存在
2. 运行 python extract_data.py
3. 运行 python final_dormitory_allocation.py
4. 检查输出文件

或者直接运行: run_allocation.bat
"""
    
    with open("测试报告.txt", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("测试报告已保存: 测试报告.txt")

def main():
    """主测试函数"""
    print("宿舍分配系统测试")
    print("=" * 50)
    
    # 运行所有测试
    test1 = test_file_existence()
    test_extracted_files()
    test2 = test_data_integrity()
    test3 = test_allocation_logic()
    
    # 生成报告
    generate_test_report()
    
    print("\n" + "=" * 50)
    if test1 and test2 and test3:
        print("✓ 所有测试通过")
        print("系统准备就绪，可以运行分配程序")
    else:
        print("✗ 部分测试失败")
        print("请检查错误信息并修复问题")
    
    print("\n推荐运行命令:")
    print("1. python extract_data.py")
    print("2. python final_dormitory_allocation.py")
    print("或运行: run_allocation.bat")

if __name__ == "__main__":
    main()
