#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI程序的基本功能
"""

import os
import sys

def test_imports():
    """测试导入"""
    print("测试导入...")
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
        
        import pandas as pd
        print("✓ pandas 导入成功")
        
        import csv
        print("✓ csv 导入成功")
        
        from collections import defaultdict
        print("✓ collections 导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_data_files():
    """测试数据文件"""
    print("\n测试数据文件...")
    
    files = [
        "法学院人员名单.csv",
        "需排除人员名单.csv",
        "已匹配的宿舍.csv", 
        "宿舍情况.csv"
    ]
    
    all_exist = True
    for file in files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"❌ {file} - 不存在")
            all_exist = False
    
    return all_exist

def test_gui_creation():
    """测试GUI创建"""
    print("\n测试GUI创建...")
    try:
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("300x200")
        
        # 添加测试组件
        label = tk.Label(root, text="GUI测试成功！")
        label.pack(pady=50)
        
        button = tk.Button(root, text="关闭", command=root.destroy)
        button.pack()
        
        print("✓ GUI组件创建成功")
        print("显示测试窗口3秒...")
        
        # 3秒后自动关闭
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        return False

def test_dormitory_gui_import():
    """测试宿舍GUI模块导入"""
    print("\n测试宿舍GUI模块...")
    try:
        from dormitory_gui import DormitoryGUI
        print("✓ DormitoryGUI 类导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    print("=== 宿舍分配系统GUI测试 ===\n")
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请安装必要的库")
        print("运行: pip install pandas openpyxl")
        return
    
    # 测试数据文件
    if not test_data_files():
        print("\n⚠️  部分数据文件缺失，但GUI仍可启动")
    
    # 测试GUI创建
    if not test_gui_creation():
        print("\n❌ GUI创建失败")
        return
    
    # 测试宿舍GUI模块
    if not test_dormitory_gui_import():
        print("\n❌ 宿舍GUI模块导入失败")
        return
    
    print("\n✅ 所有测试通过！")
    print("\n可以启动完整的GUI程序:")
    print("python dormitory_gui.py")
    print("或")
    print("python run_gui.py")

if __name__ == "__main__":
    main()
