#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析宿舍楼层分布和性别分配问题
"""

import csv
import re
from collections import defaultdict

def analyze_dormitory_distribution():
    """分析宿舍分布"""
    print("=== 宿舍楼层分布分析 ===\n")
    
    # 读取宿舍数据
    dormitory_data = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            dormitory_data.append(row)
    
    print(f"总床位数: {len(dormitory_data)}")
    
    # 按宿舍楼分组
    buildings = defaultdict(list)
    for bed in dormitory_data:
        building = bed['宿舍楼']
        buildings[building].append(bed)
    
    print(f"宿舍楼数量: {len(buildings)}")
    
    # 分析每个宿舍楼的楼层分布
    for building, beds in buildings.items():
        print(f"\n--- {building} ---")
        
        # 按楼层分组
        floors = defaultdict(list)
        for bed in beds:
            room_num = bed['房间号']
            # 提取楼层号（房间号的前1-2位）
            floor_match = re.match(r'(\d{1,2})', room_num)
            if floor_match:
                floor = floor_match.group(1)
                floors[floor].append(bed)
        
        print(f"楼层数: {len(floors)}")
        
        # 分析每个楼层的性别分布
        floor_gender_issues = []
        for floor, floor_beds in sorted(floors.items()):
            male_beds = len([b for b in floor_beds if b['性别'] == '男'])
            female_beds = len([b for b in floor_beds if b['性别'] == '女'])
            
            print(f"  {floor}楼: 总{len(floor_beds)}床位 (男:{male_beds}, 女:{female_beds})")
            
            # 检查是否有性别混合
            if male_beds > 0 and female_beds > 0:
                floor_gender_issues.append(floor)
                print(f"    ⚠️  性别混合楼层！")
        
        if floor_gender_issues:
            print(f"  问题楼层: {', '.join(floor_gender_issues)}")
        else:
            print(f"  ✓ 所有楼层性别统一")
    
    # 总体统计
    total_male = len([b for b in dormitory_data if b['性别'] == '男'])
    total_female = len([b for b in dormitory_data if b['性别'] == '女'])
    
    print(f"\n=== 总体统计 ===")
    print(f"男生床位: {total_male}")
    print(f"女生床位: {total_female}")
    print(f"总床位: {total_male + total_female}")

def analyze_student_demand():
    """分析学生需求"""
    print(f"\n=== 学生需求分析 ===")
    
    # 读取学生数据
    students = []
    with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            students.append(row)
    
    # 读取排除名单
    exclude_names = set()
    exclude_file = "需排除人员名单_utf8.csv" if os.path.exists("需排除人员名单_utf8.csv") else "需排除人员名单.csv"
    with open(exclude_file, 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            exclude_names.add(row['姓名'])
    
    # 计算实际需求
    total_students = len(students)
    male_students = len([s for s in students if s['性别'] == '男'])
    female_students = len([s for s in students if s['性别'] == '女'])
    
    exclude_male = 0
    exclude_female = 0
    for name in exclude_names:
        for student in students:
            if student['姓名'] == name:
                if student['性别'] == '男':
                    exclude_male += 1
                else:
                    exclude_female += 1
                break
    
    actual_male_need = male_students - exclude_male
    actual_female_need = female_students - exclude_female
    
    print(f"学生总数: {total_students}")
    print(f"男学生: {male_students} (排除{exclude_male}) = 实际需求{actual_male_need}")
    print(f"女学生: {female_students} (排除{exclude_female}) = 实际需求{actual_female_need}")
    
    return actual_male_need, actual_female_need

def check_capacity_mismatch():
    """检查容量不匹配问题"""
    print(f"\n=== 容量匹配检查 ===")
    
    # 床位供给
    dormitory_data = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            dormitory_data.append(row)
    
    male_beds = len([b for b in dormitory_data if b['性别'] == '男'])
    female_beds = len([b for b in dormitory_data if b['性别'] == '女'])
    
    # 学生需求
    actual_male_need, actual_female_need = analyze_student_demand()
    
    print(f"\n供需对比:")
    print(f"男生: 床位{male_beds} vs 需求{actual_male_need} = 余量{male_beds - actual_male_need}")
    print(f"女生: 床位{female_beds} vs 需求{actual_female_need} = 余量{female_beds - actual_female_need}")
    
    if male_beds < actual_male_need:
        print(f"❌ 男生床位不足 {actual_male_need - male_beds} 个")
    elif male_beds > actual_male_need:
        print(f"✓ 男生床位充足，剩余 {male_beds - actual_male_need} 个")
    
    if female_beds < actual_female_need:
        print(f"❌ 女生床位不足 {actual_female_need - female_beds} 个")
    elif female_beds > actual_female_need:
        print(f"✓ 女生床位充足，剩余 {female_beds - actual_female_need} 个")
    
    # 建议解决方案
    if female_beds < actual_female_need and male_beds > actual_male_need:
        shortage = actual_female_need - female_beds
        surplus = male_beds - actual_male_need
        convertible = min(shortage, surplus)
        
        print(f"\n💡 建议解决方案:")
        print(f"可以将 {convertible} 个男生床位转换为女生床位")
        print(f"转换后: 男生床位{male_beds - convertible}, 女生床位{female_beds + convertible}")

def suggest_floor_optimization():
    """建议楼层优化方案"""
    print(f"\n=== 楼层优化建议 ===")
    
    print("1. 楼层性别统一原则:")
    print("   - 每个楼层应该只分配给一种性别")
    print("   - 避免同楼层男女混住的安全隐患")
    
    print("\n2. 床位转换策略:")
    print("   - 识别性别混合楼层")
    print("   - 优先转换床位较少的性别")
    print("   - 保持楼层内部性别统一")
    
    print("\n3. 分配优化:")
    print("   - 先按楼层分配，再按房间分配")
    print("   - 同性别学生优先分配到同一楼层")
    print("   - 预留部分床位用于后续调整")

if __name__ == "__main__":
    import os
    
    analyze_dormitory_distribution()
    analyze_student_demand()
    check_capacity_mismatch()
    suggest_floor_optimization()
