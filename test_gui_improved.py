#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的GUI功能
"""

import os
import sys

def test_improved_allocation_import():
    """测试改进分配算法导入"""
    print("测试改进分配算法导入...")
    try:
        from improved_allocation import ImprovedDormitoryAllocator
        print("✓ ImprovedDormitoryAllocator 导入成功")
        
        # 测试创建实例
        allocator = ImprovedDormitoryAllocator()
        print("✓ 分配器实例创建成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_gui_with_improved_allocation():
    """测试GUI与改进分配算法的集成"""
    print("\n测试GUI与改进分配算法集成...")
    try:
        import tkinter as tk
        from dormitory_gui import DormitoryGUI
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建GUI实例
        app = DormitoryGUI(root)
        print("✓ GUI实例创建成功")
        
        # 检查是否有智能分配方法
        if hasattr(app, 'run_improved_allocation'):
            print("✓ 智能分配方法存在")
        else:
            print("❌ 智能分配方法不存在")
            return False
        
        # 检查是否有问题分析方法
        if hasattr(app, 'analyze_problems'):
            print("✓ 问题分析方法存在")
        else:
            print("❌ 问题分析方法不存在")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        return False

def test_data_files_for_improved_allocation():
    """测试改进分配所需的数据文件"""
    print("\n测试改进分配所需数据文件...")
    
    required_files = [
        "法学院人员名单.csv",
        "需排除人员名单.csv",
        "已匹配的宿舍.csv", 
        "宿舍情况.csv"
    ]
    
    utf8_files = [
        "需排除人员名单_utf8.csv",
        "已匹配的宿舍_utf8.csv",
        "宿舍情况_utf8.csv"
    ]
    
    all_good = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"❌ {file} - 缺失")
            all_good = False
    
    print("\nUTF-8编码文件:")
    for file in utf8_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"⚠️  {file} - 不存在（可选）")
    
    return all_good

def test_improved_allocation_execution():
    """测试改进分配算法执行"""
    print("\n测试改进分配算法执行...")
    try:
        from improved_allocation import ImprovedDormitoryAllocator
        
        # 创建分配器
        allocator = ImprovedDormitoryAllocator()
        
        # 测试数据加载
        if allocator.load_data():
            print("✓ 数据加载成功")
        else:
            print("❌ 数据加载失败")
            return False
        
        # 测试楼层分析
        allocator.analyze_floor_distribution()
        print("✓ 楼层分析完成")
        
        # 检查是否有分析结果
        if hasattr(allocator, 'floor_analysis') and allocator.floor_analysis:
            print("✓ 楼层分析结果生成")
        else:
            print("⚠️  楼层分析结果为空")
        
        return True
        
    except Exception as e:
        print(f"❌ 改进分配算法执行测试失败: {e}")
        return False

def test_output_files():
    """测试输出文件"""
    print("\n检查输出文件...")
    
    output_files = [
        "宿舍分配结果.csv",
        "改进分配结果.csv",
        "宿舍分配报告.txt",
        "改进分配报告.txt"
    ]
    
    for file in output_files:
        if os.path.exists(file):
            print(f"✓ {file} - 存在")
            # 检查文件大小
            size = os.path.getsize(file)
            print(f"  文件大小: {size} 字节")
        else:
            print(f"⚠️  {file} - 不存在（运行分配后生成）")

def main():
    print("=== 改进GUI功能测试 ===\n")
    
    # 测试改进分配算法导入
    if not test_improved_allocation_import():
        print("\n❌ 改进分配算法导入失败")
        return
    
    # 测试数据文件
    if not test_data_files_for_improved_allocation():
        print("\n⚠️  部分数据文件缺失，但仍可进行功能测试")
    
    # 测试GUI集成
    if not test_gui_with_improved_allocation():
        print("\n❌ GUI集成测试失败")
        return
    
    # 测试改进分配算法执行
    if not test_improved_allocation_execution():
        print("\n⚠️  改进分配算法执行测试失败，可能是数据问题")
    
    # 检查输出文件
    test_output_files()
    
    print("\n✅ 主要功能测试完成！")
    print("\n可以启动改进的GUI:")
    print("python dormitory_gui.py")
    print("\n新功能:")
    print("1. 点击'分析问题'查看宿舍分配问题")
    print("2. 点击'智能分配'执行改进的分配算法")
    print("3. 点击'基础分配'执行传统分配算法")

if __name__ == "__main__":
    main()
