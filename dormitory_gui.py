#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宿舍分配系统可视化界面
支持查看分配结果和手动调整宿舍
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import csv
import os
import random
from collections import defaultdict
import pandas as pd

class DormitoryGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("宿舍分配系统")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.students = []
        self.exclude_names = set()
        self.matched_students = []
        self.dormitory_beds = []
        self.allocation_result = []
        
        # 创建界面
        self.create_widgets()
        
        # 自动加载数据
        self.load_data()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮
        ttk.Button(button_frame, text="加载数据", command=self.load_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="分析问题", command=self.analyze_problems).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="完美分配", command=self.run_fixed_allocation).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="智能分配", command=self.run_improved_allocation).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="基础分配", command=self.run_allocation).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存结果", command=self.save_results).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="导出Excel", command=self.export_excel).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态标签
        self.status_label = ttk.Label(button_frame, text="就绪")
        self.status_label.pack(side=tk.RIGHT)
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 分配结果标签页
        self.create_allocation_tab(notebook)
        
        # 宿舍管理标签页
        self.create_dormitory_tab(notebook)
        
        # 统计信息标签页
        self.create_statistics_tab(notebook)
    
    def create_allocation_tab(self, notebook):
        """创建分配结果标签页"""
        allocation_frame = ttk.Frame(notebook)
        notebook.add(allocation_frame, text="分配结果")
        
        # 搜索框架
        search_frame = ttk.Frame(allocation_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side=tk.LEFT, padx=(5, 10))
        search_entry.bind('<KeyRelease>', self.filter_allocation)
        
        ttk.Button(search_frame, text="手动调整", command=self.manual_adjust).pack(side=tk.RIGHT)
        
        # 分配结果表格
        columns = ('序号', '学号', '姓名', '性别', '专业', '宿舍楼', '房间号', '床位号', '分配类型')
        self.allocation_tree = ttk.Treeview(allocation_frame, columns=columns, show='headings', height=20)
        
        # 设置列标题和排序功能
        self.allocation_sort_reverse = {}
        for col in columns:
            self.allocation_tree.heading(col, text=col, command=lambda c=col: self.sort_allocation_column(c))
            self.allocation_sort_reverse[col] = False

            if col == '序号':
                self.allocation_tree.column(col, width=50)
            elif col == '学号':
                self.allocation_tree.column(col, width=80)
            elif col == '姓名':
                self.allocation_tree.column(col, width=80)
            elif col in ['性别', '床位号']:
                self.allocation_tree.column(col, width=50)
            elif col == '专业':
                self.allocation_tree.column(col, width=60)
            elif col == '分配类型':
                self.allocation_tree.column(col, width=80)
            else:
                self.allocation_tree.column(col, width=120)
        
        # 滚动条
        allocation_scrollbar = ttk.Scrollbar(allocation_frame, orient=tk.VERTICAL, command=self.allocation_tree.yview)
        self.allocation_tree.configure(yscrollcommand=allocation_scrollbar.set)
        
        # 打包表格和滚动条
        self.allocation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        allocation_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_dormitory_tab(self, notebook):
        """创建宿舍管理标签页"""
        dormitory_frame = ttk.Frame(notebook)
        notebook.add(dormitory_frame, text="宿舍管理")
        
        # 宿舍筛选框架
        filter_frame = ttk.Frame(dormitory_frame)
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(filter_frame, text="宿舍楼:").pack(side=tk.LEFT)
        self.building_var = tk.StringVar()
        self.building_combo = ttk.Combobox(filter_frame, textvariable=self.building_var, width=20)
        self.building_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.building_combo.bind('<<ComboboxSelected>>', self.filter_dormitory)
        
        ttk.Label(filter_frame, text="性别:").pack(side=tk.LEFT)
        self.gender_var = tk.StringVar()
        gender_combo = ttk.Combobox(filter_frame, textvariable=self.gender_var, values=['全部', '男', '女'], width=10)
        gender_combo.pack(side=tk.LEFT, padx=(5, 10))
        gender_combo.set('全部')
        gender_combo.bind('<<ComboboxSelected>>', self.filter_dormitory)
        
        ttk.Label(filter_frame, text="状态:").pack(side=tk.LEFT)
        self.status_var = tk.StringVar()
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_var, values=['全部', '空置', '已分配'], width=10)
        status_combo.pack(side=tk.LEFT, padx=(5, 10))
        status_combo.set('全部')
        status_combo.bind('<<ComboboxSelected>>', self.filter_dormitory)
        
        # 宿舍表格
        dorm_columns = ('宿舍楼', '房间号', '床位号', '使用情况', '性别', '学生姓名')
        self.dormitory_tree = ttk.Treeview(dormitory_frame, columns=dorm_columns, show='headings', height=25)
        
        for col in dorm_columns:
            self.dormitory_tree.heading(col, text=col)
            if col in ['床位号', '性别']:
                self.dormitory_tree.column(col, width=60)
            elif col == '使用情况':
                self.dormitory_tree.column(col, width=80)
            elif col == '学生姓名':
                self.dormitory_tree.column(col, width=100)
            else:
                self.dormitory_tree.column(col, width=150)
        
        # 滚动条
        dorm_scrollbar = ttk.Scrollbar(dormitory_frame, orient=tk.VERTICAL, command=self.dormitory_tree.yview)
        self.dormitory_tree.configure(yscrollcommand=dorm_scrollbar.set)
        
        self.dormitory_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        dorm_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_statistics_tab(self, notebook):
        """创建统计信息标签页"""
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="统计信息")
        
        # 统计信息文本框
        self.stats_text = tk.Text(stats_frame, wrap=tk.WORD, font=('Consolas', 10))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def load_data(self):
        """加载数据"""
        try:
            self.status_label.config(text="正在加载数据...")
            self.root.update()
            
            # 清空现有数据
            self.students.clear()
            self.exclude_names.clear()
            self.matched_students.clear()
            self.dormitory_beds.clear()
            
            # 加载学生数据
            if os.path.exists("法学院人员名单.csv"):
                with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        name = row.get('姓名', '').strip()
                        gender = row.get('性别', '').strip()
                        if name and gender:
                            self.students.append(row)
            
            # 加载排除名单
            exclude_file = "需排除人员名单_utf8.csv" if os.path.exists("需排除人员名单_utf8.csv") else "需排除人员名单.csv"
            if os.path.exists(exclude_file):
                with open(exclude_file, 'r', encoding='utf-8-sig', newline='') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        name = row.get('姓名', '').strip()
                        if name:
                            self.exclude_names.add(name)
            
            # 加载已匹配学生
            matched_file = "已匹配的宿舍_utf8.csv" if os.path.exists("已匹配的宿舍_utf8.csv") else "已匹配的宿舍.csv"
            if os.path.exists(matched_file):
                with open(matched_file, 'r', encoding='utf-8-sig', newline='') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        self.matched_students.append(row)
            
            # 加载宿舍信息
            dorm_file = "宿舍情况_utf8.csv" if os.path.exists("宿舍情况_utf8.csv") else "宿舍情况.csv"
            if os.path.exists(dorm_file):
                with open(dorm_file, 'r', encoding='utf-8-sig', newline='') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        self.dormitory_beds.append(row)
            
            # 更新界面
            self.update_dormitory_display()
            self.update_building_combo()
            self.update_statistics()
            
            # 尝试加载现有分配结果
            if os.path.exists("宿舍分配结果.csv"):
                self.load_allocation_results()
            
            self.status_label.config(text=f"数据加载完成 - 学生:{len(self.students)} 床位:{len(self.dormitory_beds)}")
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据时出错: {e}")
            self.status_label.config(text="加载失败")
    
    def load_allocation_results(self):
        """加载现有分配结果"""
        try:
            self.allocation_result.clear()
            with open("宿舍分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    self.allocation_result.append(row)
            
            self.update_allocation_display()
            self.update_dormitory_display()  # 更新宿舍显示以反映分配状态
            
        except Exception as e:
            print(f"加载分配结果时出错: {e}")
    
    def run_allocation(self):
        """执行分配"""
        try:
            self.status_label.config(text="正在执行分配...")
            self.root.update()
            
            # 清空现有分配结果
            self.allocation_result.clear()
            
            # 重置宿舍状态
            for bed in self.dormitory_beds:
                if bed.get('使用情况') == '已分配':
                    bed['使用情况'] = '空置'
            
            # 执行分配逻辑（从simplified_allocation.py复制）
            self.allocate_matched_students()
            self.allocate_remaining_students()
            
            # 更新显示
            self.update_allocation_display()
            self.update_dormitory_display()
            self.update_statistics()
            
            self.status_label.config(text=f"分配完成 - 已分配:{len(self.allocation_result)}人")
            messagebox.showinfo("成功", f"分配完成！共分配 {len(self.allocation_result)} 名学生")
            
        except Exception as e:
            messagebox.showerror("错误", f"执行分配时出错: {e}")
            self.status_label.config(text="分配失败")

    def run_improved_allocation(self):
        """执行改进的智能分配"""
        try:
            self.status_label.config(text="正在执行智能分配...")
            self.root.update()

            # 导入改进的分配算法
            from improved_allocation import ImprovedDormitoryAllocator

            # 创建分配器实例
            allocator = ImprovedDormitoryAllocator()

            # 使用当前GUI的数据
            allocator.students = self.students.copy()
            allocator.exclude_names = self.exclude_names.copy()
            allocator.matched_students = self.matched_students.copy()
            allocator.dormitory_beds = [bed.copy() for bed in self.dormitory_beds]

            # 执行改进的分配流程
            allocator.analyze_floor_distribution()
            allocator.resolve_gender_conflicts()
            allocator.optimize_allocation_by_floor()

            # 获取分配结果
            self.allocation_result = allocator.allocation_result.copy()
            self.dormitory_beds = allocator.dormitory_beds.copy()

            # 更新显示
            self.update_allocation_display()
            self.update_dormitory_display()
            self.update_statistics()

            # 显示结果
            total_allocated = len(self.allocation_result)
            matched_count = len([r for r in self.allocation_result if r.get('分配类型') == '已匹配'])
            random_count = len([r for r in self.allocation_result if r.get('分配类型') == '随机分配'])

            result_msg = f"""智能分配完成！

总分配人数: {total_allocated}
已匹配分配: {matched_count}
随机分配: {random_count}

✅ 已解决楼层性别混合问题
✅ 已优化床位供需分配"""

            self.status_label.config(text=f"智能分配完成 - 已分配:{total_allocated}人")
            messagebox.showinfo("智能分配成功", result_msg)

        except ImportError:
            messagebox.showerror("错误", "找不到改进分配算法模块，请确保 improved_allocation.py 文件存在")
        except Exception as e:
            messagebox.showerror("错误", f"执行智能分配时出错: {e}")
            self.status_label.config(text="智能分配失败")

    def run_fixed_allocation(self):
        """执行修复版完美分配"""
        try:
            self.status_label.config(text="正在执行完美分配...")
            self.root.update()

            # 导入修复版分配算法
            from fixed_allocation import FixedDormitoryAllocator

            # 创建分配器实例
            allocator = FixedDormitoryAllocator()

            # 执行分配
            if allocator.run():
                # 加载分配结果
                self.allocation_result.clear()

                try:
                    with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
                        reader = csv.DictReader(f)
                        for row in reader:
                            self.allocation_result.append(row)

                    # 更新床位状态
                    for bed in self.dormitory_beds:
                        bed['使用情况'] = '空置'

                    # 标记已分配床位
                    for result in self.allocation_result:
                        for bed in self.dormitory_beds:
                            if (bed.get('宿舍楼') == result.get('宿舍楼') and
                                bed.get('房间号') == result.get('房间号') and
                                str(bed.get('床位号')) == str(result.get('床位号'))):
                                bed['使用情况'] = '已分配'
                                break

                    # 更新显示
                    self.update_allocation_display()
                    self.update_dormitory_display()
                    self.update_statistics()

                    # 显示结果
                    total_allocated = len(self.allocation_result)
                    matched_count = len([r for r in self.allocation_result if r.get('分配类型') == '已匹配'])
                    random_count = len([r for r in self.allocation_result if r.get('分配类型') == '随机分配'])

                    result_msg = f"""完美分配成功！

总分配人数: {total_allocated}
已匹配分配: {matched_count}
随机分配: {random_count}

✅ 床位唯一性保证
✅ 学生唯一性保证
✅ 数据完整性验证通过
✅ 床位利用率最大化
✅ 已匹配学生优先分配到同一房间
✅ 性别冲突智能解决"""

                    self.status_label.config(text=f"完美分配完成 - 已分配:{total_allocated}人")
                    messagebox.showinfo("完美分配成功", result_msg)

                except Exception as e:
                    messagebox.showerror("错误", f"加载分配结果时出错: {e}")
            else:
                messagebox.showerror("错误", "完美分配执行失败")

        except ImportError:
            messagebox.showerror("错误", "找不到修复分配算法模块，请确保 fixed_allocation.py 文件存在")
        except Exception as e:
            messagebox.showerror("错误", f"执行完美分配时出错: {e}")
            self.status_label.config(text="完美分配失败")

    def allocate_matched_students(self):
        """分配已匹配学生"""
        # 按宿舍组分组
        matched_groups = defaultdict(list)
        for student in self.matched_students:
            dorm_type = student.get('类型', '')
            dorm_num = student.get('宿舍序号', '')
            if dorm_num and str(dorm_num).strip() and str(dorm_num).strip() != 'nan':
                key = f"{dorm_type}_{dorm_num}"
                matched_groups[key].append(student)
        
        # 获取可用床位
        available_beds = [b for b in self.dormitory_beds if b.get('使用情况') == '空置']
        male_beds = [b for b in available_beds if b.get('性别') == '男']
        female_beds = [b for b in available_beds if b.get('性别') == '女']
        
        used_bed_indices = {'male': set(), 'female': set()}
        
        for group_key, group_students in matched_groups.items():
            if not group_students:
                continue
                
            gender = group_students[0].get('性别')
            group_size = len(group_students)
            
            # 选择床位
            target_beds = male_beds if gender == '男' else female_beds
            bed_type = 'male' if gender == '男' else 'female'
            selected_beds = []
            
            for i, bed in enumerate(target_beds):
                if i not in used_bed_indices[bed_type]:
                    selected_beds.append((i, bed))
                    used_bed_indices[bed_type].add(i)
                    if len(selected_beds) >= group_size:
                        break
            
            # 分配床位
            for i, student in enumerate(group_students):
                if i < len(selected_beds):
                    _, bed = selected_beds[i]
                    self.allocation_result.append({
                        '学号': self._get_student_id(student.get('姓名', '')),
                        '姓名': student.get('姓名', ''),
                        '性别': student.get('性别', ''),
                        '专业': self._get_student_major(student.get('姓名', '')),
                        '宿舍楼': bed.get('宿舍楼', ''),
                        '房间号': bed.get('房间号', ''),
                        '床位号': bed.get('床位号', ''),
                        '分配类型': '已匹配'
                    })
                    bed['使用情况'] = '已分配'
    
    def allocate_remaining_students(self):
        """分配剩余学生"""
        # 获取需要分配的学生
        matched_names = set(s.get('姓名', '') for s in self.matched_students)
        
        remaining_students = []
        for student in self.students:
            name = student['姓名']
            if name not in self.exclude_names and name not in matched_names:
                remaining_students.append(student)
        
        male_remaining = [s for s in remaining_students if s['性别'] == '男']
        female_remaining = [s for s in remaining_students if s['性别'] == '女']
        
        # 获取剩余床位
        available_beds = [b for b in self.dormitory_beds if b.get('使用情况') == '空置']
        male_beds = [b for b in available_beds if b.get('性别') == '男']
        female_beds = [b for b in available_beds if b.get('性别') == '女']
        
        # 随机分配
        random.seed(42)
        random.shuffle(male_remaining)
        random.shuffle(male_beds)
        random.shuffle(female_remaining)
        random.shuffle(female_beds)
        
        # 分配男生
        for i, student in enumerate(male_remaining):
            if i < len(male_beds):
                bed = male_beds[i]
                self.allocation_result.append({
                    '学号': student['学号'],
                    '姓名': student['姓名'],
                    '性别': student['性别'],
                    '专业': student['专业'],
                    '宿舍楼': bed.get('宿舍楼', ''),
                    '房间号': bed.get('房间号', ''),
                    '床位号': bed.get('床位号', ''),
                    '分配类型': '随机分配'
                })
                bed['使用情况'] = '已分配'
        
        # 分配女生
        for i, student in enumerate(female_remaining):
            if i < len(female_beds):
                bed = female_beds[i]
                self.allocation_result.append({
                    '学号': student['学号'],
                    '姓名': student['姓名'],
                    '性别': student['性别'],
                    '专业': student['专业'],
                    '宿舍楼': bed.get('宿舍楼', ''),
                    '房间号': bed.get('房间号', ''),
                    '床位号': bed.get('床位号', ''),
                    '分配类型': '随机分配'
                })
                bed['使用情况'] = '已分配'
    
    def _get_student_id(self, name):
        """根据姓名获取学号"""
        for student in self.students:
            if student['姓名'] == name:
                return student.get('学号', '')
        return ''
    
    def _get_student_major(self, name):
        """根据姓名获取专业"""
        for student in self.students:
            if student['姓名'] == name:
                return student.get('专业', '')
        return ''

    def update_allocation_display(self):
        """更新分配结果显示"""
        # 清空现有数据
        for item in self.allocation_tree.get_children():
            self.allocation_tree.delete(item)

        # 添加分配结果
        for i, result in enumerate(self.allocation_result, 1):
            self.allocation_tree.insert('', 'end', values=(
                i,  # 序号
                result.get('学号', ''),
                result.get('姓名', ''),
                result.get('性别', ''),
                result.get('专业', ''),
                result.get('宿舍楼', ''),
                result.get('房间号', ''),
                result.get('床位号', ''),
                result.get('分配类型', '')
            ))

    def update_dormitory_display(self):
        """更新宿舍显示"""
        # 清空现有数据
        for item in self.dormitory_tree.get_children():
            self.dormitory_tree.delete(item)

        # 创建学生姓名映射
        student_map = {}
        for result in self.allocation_result:
            key = f"{result.get('宿舍楼', '')}_{result.get('房间号', '')}_{result.get('床位号', '')}"
            student_map[key] = result.get('姓名', '')

        # 添加宿舍数据
        for bed in self.dormitory_beds:
            key = f"{bed.get('宿舍楼', '')}_{bed.get('房间号', '')}_{bed.get('床位号', '')}"
            student_name = student_map.get(key, '')

            self.dormitory_tree.insert('', 'end', values=(
                bed.get('宿舍楼', ''),
                bed.get('房间号', ''),
                bed.get('床位号', ''),
                bed.get('使用情况', ''),
                bed.get('性别', ''),
                student_name
            ))

    def update_building_combo(self):
        """更新宿舍楼下拉框"""
        buildings = set()
        for bed in self.dormitory_beds:
            building = bed.get('宿舍楼', '')
            if building:
                buildings.add(building)

        self.building_combo['values'] = ['全部'] + sorted(list(buildings))
        self.building_combo.set('全部')

    def update_statistics(self):
        """更新统计信息"""
        stats_text = "=== 宿舍分配统计信息 ===\n\n"

        # 基本统计
        total_students = len(self.students)
        exclude_count = len(self.exclude_names)
        matched_count = len(self.matched_students)
        allocated_count = len(self.allocation_result)

        stats_text += f"学生总数: {total_students}\n"
        stats_text += f"需排除: {exclude_count}\n"
        stats_text += f"已匹配: {matched_count}\n"
        stats_text += f"已分配: {allocated_count}\n\n"

        # 床位统计
        total_beds = len(self.dormitory_beds)
        male_beds = len([b for b in self.dormitory_beds if b.get('性别') == '男'])
        female_beds = len([b for b in self.dormitory_beds if b.get('性别') == '女'])
        used_beds = len([b for b in self.dormitory_beds if b.get('使用情况') == '已分配'])

        stats_text += f"床位总数: {total_beds}\n"
        stats_text += f"男生床位: {male_beds}\n"
        stats_text += f"女生床位: {female_beds}\n"
        stats_text += f"已使用: {used_beds}\n"
        stats_text += f"剩余床位: {total_beds - used_beds}\n\n"

        # 分配统计
        if self.allocation_result:
            male_allocated = len([r for r in self.allocation_result if r.get('性别') == '男'])
            female_allocated = len([r for r in self.allocation_result if r.get('性别') == '女'])
            matched_allocated = len([r for r in self.allocation_result if r.get('分配类型') == '已匹配'])
            random_allocated = len([r for r in self.allocation_result if r.get('分配类型') == '随机分配'])

            stats_text += "分配详情:\n"
            stats_text += f"  男生: {male_allocated}\n"
            stats_text += f"  女生: {female_allocated}\n"
            stats_text += f"  已匹配分配: {matched_allocated}\n"
            stats_text += f"  随机分配: {random_allocated}\n\n"

        # 宿舍楼统计
        building_stats = defaultdict(lambda: {'total': 0, 'used': 0, 'male': 0, 'female': 0})
        for bed in self.dormitory_beds:
            building = bed.get('宿舍楼', '')
            building_stats[building]['total'] += 1
            if bed.get('使用情况') == '已分配':
                building_stats[building]['used'] += 1
            if bed.get('性别') == '男':
                building_stats[building]['male'] += 1
            else:
                building_stats[building]['female'] += 1

        stats_text += "各宿舍楼统计:\n"
        for building, stats in sorted(building_stats.items()):
            utilization = (stats['used'] / stats['total'] * 100) if stats['total'] > 0 else 0
            stats_text += f"  {building}:\n"
            stats_text += f"    总床位: {stats['total']} (男:{stats['male']}, 女:{stats['female']})\n"
            stats_text += f"    已使用: {stats['used']}\n"
            stats_text += f"    利用率: {utilization:.1f}%\n\n"

        # 更新文本框
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def sort_allocation_column(self, col):
        """排序分配结果列"""
        # 获取当前数据
        data = []
        for child in self.allocation_tree.get_children():
            values = self.allocation_tree.item(child)['values']
            data.append(values)

        # 确定排序列的索引
        columns = ('序号', '学号', '姓名', '性别', '专业', '宿舍楼', '房间号', '床位号', '分配类型')
        col_index = columns.index(col)

        # 排序
        reverse = self.allocation_sort_reverse[col]
        if col in ['序号', '学号', '房间号', '床位号']:
            # 数字排序
            data.sort(key=lambda x: int(str(x[col_index]).replace('.0', '')) if str(x[col_index]).replace('.0', '').isdigit() else 0, reverse=reverse)
        else:
            # 文本排序
            data.sort(key=lambda x: str(x[col_index]), reverse=reverse)

        # 更新排序状态
        self.allocation_sort_reverse[col] = not reverse

        # 重新插入数据
        for item in self.allocation_tree.get_children():
            self.allocation_tree.delete(item)

        for i, values in enumerate(data):
            # 更新序号
            new_values = list(values)
            new_values[0] = i + 1
            self.allocation_tree.insert('', 'end', values=new_values)

    def filter_allocation(self, event=None):
        """筛选分配结果"""
        search_term = self.search_var.get().lower()

        # 清空现有显示
        for item in self.allocation_tree.get_children():
            self.allocation_tree.delete(item)

        # 添加筛选后的结果
        index = 1
        for result in self.allocation_result:
            # 扩展搜索范围：姓名、学号、专业、性别、宿舍楼、房间号、分配类型
            if (search_term in result.get('姓名', '').lower() or
                search_term in result.get('学号', '').lower() or
                search_term in result.get('专业', '').lower() or
                search_term in result.get('性别', '').lower() or
                search_term in result.get('宿舍楼', '').lower() or
                search_term in result.get('房间号', '').lower() or
                search_term in result.get('分配类型', '').lower()):

                self.allocation_tree.insert('', 'end', values=(
                    index,
                    result.get('学号', ''),
                    result.get('姓名', ''),
                    result.get('性别', ''),
                    result.get('专业', ''),
                    result.get('宿舍楼', ''),
                    result.get('房间号', ''),
                    result.get('床位号', ''),
                    result.get('分配类型', '')
                ))
                index += 1

    def filter_dormitory(self, event=None):
        """筛选宿舍信息"""
        building = self.building_var.get()
        gender = self.gender_var.get()
        status = self.status_var.get()

        # 清空现有显示
        for item in self.dormitory_tree.get_children():
            self.dormitory_tree.delete(item)

        # 创建学生姓名映射
        student_map = {}
        for result in self.allocation_result:
            key = f"{result.get('宿舍楼', '')}_{result.get('房间号', '')}_{result.get('床位号', '')}"
            student_map[key] = result.get('姓名', '')

        # 添加筛选后的宿舍数据
        for bed in self.dormitory_beds:
            # 应用筛选条件
            if building != '全部' and bed.get('宿舍楼', '') != building:
                continue
            if gender != '全部' and bed.get('性别', '') != gender:
                continue
            if status != '全部' and bed.get('使用情况', '') != status:
                continue

            key = f"{bed.get('宿舍楼', '')}_{bed.get('房间号', '')}_{bed.get('床位号', '')}"
            student_name = student_map.get(key, '')

            self.dormitory_tree.insert('', 'end', values=(
                bed.get('宿舍楼', ''),
                bed.get('房间号', ''),
                bed.get('床位号', ''),
                bed.get('使用情况', ''),
                bed.get('性别', ''),
                student_name
            ))

    def manual_adjust(self):
        """手动调整宿舍"""
        selection = self.allocation_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要调整的学生")
            return

        # 获取选中的学生信息
        item = selection[0]
        values = self.allocation_tree.item(item, 'values')
        student_name = values[1]
        student_gender = values[2]

        # 创建调整窗口
        self.create_adjust_window(student_name, student_gender)

    def create_adjust_window(self, student_name, student_gender):
        """创建调整窗口"""
        adjust_window = tk.Toplevel(self.root)
        adjust_window.title(f"调整 {student_name} 的宿舍")
        adjust_window.geometry("600x400")
        adjust_window.transient(self.root)
        adjust_window.grab_set()

        # 说明标签
        ttk.Label(adjust_window, text=f"为 {student_name} ({student_gender}) 选择新宿舍:").pack(pady=10)

        # 可用床位表格
        columns = ('宿舍楼', '房间号', '床位号', '使用情况')
        available_tree = ttk.Treeview(adjust_window, columns=columns, show='headings', height=15)

        for col in columns:
            available_tree.heading(col, text=col)
            if col == '床位号':
                available_tree.column(col, width=60)
            elif col == '使用情况':
                available_tree.column(col, width=80)
            else:
                available_tree.column(col, width=150)

        # 添加可用床位（同性别的空置床位）
        for bed in self.dormitory_beds:
            if bed.get('性别') == student_gender and bed.get('使用情况') == '空置':
                available_tree.insert('', 'end', values=(
                    bed.get('宿舍楼', ''),
                    bed.get('房间号', ''),
                    bed.get('床位号', ''),
                    bed.get('使用情况', '')
                ))

        available_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 按钮框架
        button_frame = ttk.Frame(adjust_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def confirm_adjust():
            selection = available_tree.selection()
            if not selection:
                messagebox.showwarning("警告", "请选择新的床位")
                return

            # 获取新床位信息
            item = selection[0]
            values = available_tree.item(item, 'values')
            new_building = values[0]
            new_room = values[1]
            new_bed = values[2]

            # 执行调整
            self.perform_manual_adjust(student_name, new_building, new_room, new_bed)
            adjust_window.destroy()

        ttk.Button(button_frame, text="确认调整", command=confirm_adjust).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=adjust_window.destroy).pack(side=tk.LEFT)

    def perform_manual_adjust(self, student_name, new_building, new_room, new_bed):
        """执行手动调整"""
        try:
            # 找到学生的当前分配
            old_allocation = None
            for i, result in enumerate(self.allocation_result):
                if result.get('姓名') == student_name:
                    old_allocation = result
                    break

            if not old_allocation:
                messagebox.showerror("错误", "找不到学生的分配记录")
                return

            # 释放原床位
            old_key = f"{old_allocation.get('宿舍楼')}_{old_allocation.get('房间号')}_{old_allocation.get('床位号')}"
            for bed in self.dormitory_beds:
                bed_key = f"{bed.get('宿舍楼')}_{bed.get('房间号')}_{bed.get('床位号')}"
                if bed_key == old_key:
                    bed['使用情况'] = '空置'
                    break

            # 占用新床位
            new_key = f"{new_building}_{new_room}_{new_bed}"
            for bed in self.dormitory_beds:
                bed_key = f"{bed.get('宿舍楼')}_{bed.get('房间号')}_{bed.get('床位号')}"
                if bed_key == new_key:
                    bed['使用情况'] = '已分配'
                    break

            # 更新分配记录
            old_allocation['宿舍楼'] = new_building
            old_allocation['房间号'] = new_room
            old_allocation['床位号'] = new_bed
            old_allocation['分配类型'] = '手动调整'

            # 更新显示
            self.update_allocation_display()
            self.update_dormitory_display()
            self.update_statistics()

            messagebox.showinfo("成功", f"已将 {student_name} 调整到 {new_building} {new_room}室 {new_bed}号床位")

        except Exception as e:
            messagebox.showerror("错误", f"调整失败: {e}")

    def save_results(self):
        """保存分配结果"""
        if not self.allocation_result:
            messagebox.showwarning("警告", "没有分配结果可保存")
            return

        try:
            # 保存分配结果
            with open("宿舍分配结果.csv", 'w', encoding='utf-8-sig', newline='') as f:
                fieldnames = ['学号', '姓名', '性别', '专业', '宿舍楼', '房间号', '床位号', '分配类型']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.allocation_result)

            # 生成统计报告
            self.generate_report()

            messagebox.showinfo("成功", "分配结果已保存到 宿舍分配结果.csv")
            self.status_label.config(text="结果已保存")

        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")

    def generate_report(self):
        """生成统计报告"""
        total = len(self.allocation_result)
        male_count = len([r for r in self.allocation_result if r['性别'] == '男'])
        female_count = len([r for r in self.allocation_result if r['性别'] == '女'])
        matched_count = len([r for r in self.allocation_result if r['分配类型'] == '已匹配'])
        random_count = len([r for r in self.allocation_result if r['分配类型'] == '随机分配'])
        manual_count = len([r for r in self.allocation_result if r['分配类型'] == '手动调整'])

        remaining_beds = len([b for b in self.dormitory_beds if b.get('使用情况') == '空置'])

        report = f"""=== 宿舍分配统计报告 ===

分配总结:
- 总分配人数: {total}
- 男生: {male_count} 人
- 女生: {female_count} 人

按分配方式:
- 已匹配分配: {matched_count} 人
- 随机分配: {random_count} 人
- 手动调整: {manual_count} 人

剩余床位: {remaining_beds} 个

生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        with open("宿舍分配报告.txt", 'w', encoding='utf-8') as f:
            f.write(report)

    def export_excel(self):
        """导出到Excel"""
        if not self.allocation_result:
            messagebox.showwarning("警告", "没有分配结果可导出")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="保存Excel文件"
            )

            if filename:
                # 创建DataFrame
                df = pd.DataFrame(self.allocation_result)

                # 保存到Excel
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='分配结果', index=False)

                    # 创建宿舍统计表
                    dorm_data = []
                    student_map = {}
                    for result in self.allocation_result:
                        key = f"{result.get('宿舍楼', '')}_{result.get('房间号', '')}_{result.get('床位号', '')}"
                        student_map[key] = result.get('姓名', '')

                    for bed in self.dormitory_beds:
                        key = f"{bed.get('宿舍楼', '')}_{bed.get('房间号', '')}_{bed.get('床位号', '')}"
                        student_name = student_map.get(key, '')
                        dorm_data.append({
                            '宿舍楼': bed.get('宿舍楼', ''),
                            '房间号': bed.get('房间号', ''),
                            '床位号': bed.get('床位号', ''),
                            '使用情况': bed.get('使用情况', ''),
                            '性别': bed.get('性别', ''),
                            '学生姓名': student_name
                        })

                    dorm_df = pd.DataFrame(dorm_data)
                    dorm_df.to_excel(writer, sheet_name='宿舍情况', index=False)

                messagebox.showinfo("成功", f"已导出到 {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def analyze_problems(self):
        """分析宿舍分配问题"""
        if not self.dormitory_beds:
            messagebox.showwarning("警告", "请先加载数据")
            return

        try:
            # 创建问题分析窗口
            analysis_window = tk.Toplevel(self.root)
            analysis_window.title("宿舍分配问题分析")
            analysis_window.geometry("800x600")
            analysis_window.transient(self.root)

            # 创建文本框显示分析结果
            text_frame = ttk.Frame(analysis_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            analysis_text = tk.Text(text_frame, wrap=tk.WORD, font=('Consolas', 10))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=analysis_text.yview)
            analysis_text.configure(yscrollcommand=scrollbar.set)

            analysis_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 执行分析
            analysis_result = self.perform_problem_analysis()

            # 显示分析结果
            analysis_text.insert(1.0, analysis_result)
            analysis_text.config(state=tk.DISABLED)

            # 按钮框架
            button_frame = ttk.Frame(analysis_window)
            button_frame.pack(fill=tk.X, padx=10, pady=10)

            ttk.Button(button_frame, text="关闭", command=analysis_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {e}")

    def perform_problem_analysis(self):
        """执行问题分析"""
        import re
        from collections import defaultdict

        analysis = "=== 宿舍分配问题分析报告 ===\n\n"

        # 1. 容量分析
        analysis += "1. 容量分析\n"
        analysis += "-" * 40 + "\n"

        total_beds = len(self.dormitory_beds)
        male_beds = len([b for b in self.dormitory_beds if b.get('性别') == '男'])
        female_beds = len([b for b in self.dormitory_beds if b.get('性别') == '女'])

        analysis += f"总床位数: {total_beds}\n"
        analysis += f"男生床位: {male_beds}\n"
        analysis += f"女生床位: {female_beds}\n\n"

        # 计算学生需求
        total_students = len(self.students)
        male_students = len([s for s in self.students if s.get('性别') == '男'])
        female_students = len([s for s in self.students if s.get('性别') == '女'])

        # 排除学生
        exclude_male = 0
        exclude_female = 0
        for name in self.exclude_names:
            for student in self.students:
                if student['姓名'] == name:
                    if student['性别'] == '男':
                        exclude_male += 1
                    else:
                        exclude_female += 1
                    break

        actual_male_need = male_students - exclude_male
        actual_female_need = female_students - exclude_female

        analysis += f"学生需求:\n"
        analysis += f"男学生: {male_students} (排除{exclude_male}) = 实际需求{actual_male_need}\n"
        analysis += f"女学生: {female_students} (排除{exclude_female}) = 实际需求{actual_female_need}\n\n"

        analysis += f"供需对比:\n"
        male_balance = male_beds - actual_male_need
        female_balance = female_beds - actual_female_need

        if male_balance >= 0:
            analysis += f"男生: 床位充足，剩余 {male_balance} 个\n"
        else:
            analysis += f"男生: 床位不足 {-male_balance} 个 ❌\n"

        if female_balance >= 0:
            analysis += f"女生: 床位充足，剩余 {female_balance} 个\n"
        else:
            analysis += f"女生: 床位不足 {-female_balance} 个 ❌\n"

        # 2. 楼层性别分布分析
        analysis += f"\n2. 楼层性别分布分析\n"
        analysis += "-" * 40 + "\n"

        # 按宿舍楼和楼层分组
        building_floors = defaultdict(lambda: defaultdict(list))

        for bed in self.dormitory_beds:
            building = bed['宿舍楼']
            room_num = bed['房间号']

            # 提取楼层号
            floor_match = re.match(r'(\d{1,2})', room_num)
            if floor_match:
                floor = floor_match.group(1)
                building_floors[building][floor].append(bed)

        mixed_floors = []

        for building, floors in building_floors.items():
            analysis += f"\n{building}:\n"
            for floor, beds in sorted(floors.items()):
                male_count = len([b for b in beds if b['性别'] == '男'])
                female_count = len([b for b in beds if b['性别'] == '女'])

                analysis += f"  {floor}楼: {len(beds)}床位 (男:{male_count}, 女:{female_count})"

                if male_count > 0 and female_count > 0:
                    analysis += " ⚠️ 性别混合楼层"
                    mixed_floors.append(f"{building}-{floor}楼")

                analysis += "\n"

        if mixed_floors:
            analysis += f"\n❌ 发现 {len(mixed_floors)} 个性别混合楼层:\n"
            for floor in mixed_floors:
                analysis += f"  - {floor}\n"
        else:
            analysis += f"\n✅ 所有楼层性别统一\n"

        # 3. 解决方案建议
        analysis += f"\n3. 解决方案建议\n"
        analysis += "-" * 40 + "\n"

        if female_balance < 0 and male_balance > 0:
            shortage = -female_balance
            surplus = male_balance
            convertible = min(shortage, surplus)

            analysis += f"建议将 {convertible} 个男生床位转换为女生床位:\n"
            analysis += f"  - 优先转换性别混合楼层中的男生床位\n"
            analysis += f"  - 确保转换后楼层性别统一\n"
            analysis += f"  - 转换后: 男生床位{male_beds - convertible}, 女生床位{female_beds + convertible}\n"

        if mixed_floors:
            analysis += f"\n楼层性别统一建议:\n"
            for floor in mixed_floors:
                analysis += f"  - {floor}: 建议统一为单一性别楼层\n"

        analysis += f"\n4. 数据质量问题\n"
        analysis += "-" * 40 + "\n"

        # 检查数据质量
        empty_bed_nums = len([b for b in self.dormitory_beds if not b.get('床位号') or str(b.get('床位号')).strip() == ''])
        if empty_bed_nums > 0:
            analysis += f"❌ 发现 {empty_bed_nums} 个床位号为空的记录\n"
        else:
            analysis += f"✅ 所有床位号完整\n"

        # 检查使用情况字段
        status_values = set(b.get('使用情况', '') for b in self.dormitory_beds)
        standard_status = {'空置', '已分配', '在用'}
        non_standard = status_values - standard_status
        if non_standard:
            analysis += f"⚠️ 发现非标准使用情况值: {non_standard}\n"
        else:
            analysis += f"✅ 使用情况字段规范\n"

        return analysis

def main():
    root = tk.Tk()
    app = DormitoryGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
