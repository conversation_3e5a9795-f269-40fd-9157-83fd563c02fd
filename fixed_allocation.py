#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版宿舍分配系统
解决床位重复分配和学生重名问题
"""

import csv
import random
import re
import os
from collections import defaultdict

class FixedDormitoryAllocator:
    def __init__(self):
        self.students = []
        self.exclude_names = set()
        self.matched_students = []
        self.dormitory_beds = []
        self.allocation_result = []
        self.used_beds = set()  # 跟踪已使用的床位
        self.allocated_students = set()  # 跟踪已分配的学生
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        
        # 加载学生数据
        with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                gender = row.get('性别', '').strip()
                student_id = row.get('学号', '').strip()
                if name and gender and student_id:
                    self.students.append(row)
        
        # 加载排除名单
        exclude_file = "需排除人员名单_utf8.csv" if os.path.exists("需排除人员名单_utf8.csv") else "需排除人员名单.csv"
        with open(exclude_file, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                if name:
                    self.exclude_names.add(name)
        
        # 加载已匹配学生
        matched_file = "已匹配的宿舍_utf8.csv" if os.path.exists("已匹配的宿舍_utf8.csv") else "已匹配的宿舍.csv"
        with open(matched_file, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                self.matched_students.append(row)
        
        # 加载宿舍信息 - 优先使用UTF8版本
        dorm_file = "宿舍情况_utf8.csv" if os.path.exists("宿舍情况_utf8.csv") else "宿舍情况.csv"
        with open(dorm_file, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 只加载有效的床位数据
                if (row.get('床位号') and
                    str(row.get('床位号')).strip() and
                    str(row.get('床位号')).strip() != 'nan' and
                    row.get('使用情况') == '空置'):
                    self.dormitory_beds.append(row)
        
        print(f"✓ 学生: {len(self.students)}")
        print(f"✓ 排除: {len(self.exclude_names)}")
        print(f"✓ 已匹配: {len(self.matched_students)}")
        print(f"✓ 可用床位: {len(self.dormitory_beds)}")
        
        return True
    
    def get_bed_key(self, bed):
        """获取床位唯一标识"""
        return f"{bed['宿舍楼']}_{bed['房间号']}_{bed['床位号']}"
    
    def get_student_by_name(self, name):
        """根据姓名获取学生信息（处理重名）"""
        matching_students = [s for s in self.students if s['姓名'] == name]
        
        if len(matching_students) == 1:
            return matching_students[0]
        elif len(matching_students) > 1:
            # 如果有重名，优先选择未分配的学生
            for student in matching_students:
                if student['学号'] not in self.allocated_students:
                    return student
            # 如果都已分配，返回第一个
            return matching_students[0]
        else:
            return None
    
    def allocate_bed_to_student(self, student, bed, allocation_type):
        """分配床位给学生"""
        bed_key = self.get_bed_key(bed)
        student_id = student['学号']
        
        # 检查床位是否已被使用
        if bed_key in self.used_beds:
            print(f"⚠️  床位 {bed_key} 已被使用，跳过")
            return False
        
        # 检查学生是否已被分配
        if student_id in self.allocated_students:
            print(f"⚠️  学生 {student['姓名']}({student_id}) 已被分配，跳过")
            return False
        
        # 执行分配
        self.allocation_result.append({
            '学号': student['学号'],
            '姓名': student['姓名'],
            '性别': student['性别'],
            '专业': student['专业'],
            '宿舍楼': bed['宿舍楼'],
            '房间号': bed['房间号'],
            '床位号': bed['床位号'],
            '分配类型': allocation_type
        })
        
        # 标记为已使用
        self.used_beds.add(bed_key)
        self.allocated_students.add(student_id)
        
        return True
    
    def resolve_gender_conflicts(self):
        """解决性别冲突（简化版）"""
        print("\n=== 解决性别冲突 ===")
        
        # 计算学生需求
        male_need = len([s for s in self.students 
                        if s['性别'] == '男' and s['姓名'] not in self.exclude_names])
        female_need = len([s for s in self.students 
                          if s['性别'] == '女' and s['姓名'] not in self.exclude_names])
        
        male_beds = len([b for b in self.dormitory_beds if b['性别'] == '男'])
        female_beds = len([b for b in self.dormitory_beds if b['性别'] == '女'])
        
        print(f"需求: 男生{male_need}, 女生{female_need}")
        print(f"床位: 男生{male_beds}, 女生{female_beds}")
        
        # 如果女生床位不足，转换更多男生床位
        if female_need > female_beds:
            # 计算需要转换的床位数，确保有足够的女生床位
            shortage = female_need - female_beds
            # 保留足够的男生床位，其余都可以转换
            male_surplus = male_beds - male_need
            # 实际转换数量：取需要数量和可转换数量的较小值
            need_convert = min(shortage, male_surplus) if male_surplus > 0 else shortage

            print(f"女生床位不足 {shortage} 个，男生床位过剩 {male_surplus} 个")
            print(f"计划转换 {need_convert} 个男生床位为女生床位")

            converted = 0

            for bed in self.dormitory_beds:
                if bed['性别'] == '男' and converted < need_convert:
                    bed['性别'] = '女'
                    converted += 1
                    print(f"转换床位: {self.get_bed_key(bed)} 男→女")

            print(f"✓ 转换了 {converted} 个床位")

            # 重新计算床位分布
            new_male_beds = len([b for b in self.dormitory_beds if b['性别'] == '男'])
            new_female_beds = len([b for b in self.dormitory_beds if b['性别'] == '女'])
            print(f"转换后床位分布: 男生{new_male_beds}, 女生{new_female_beds}")
        else:
            print(f"女生床位充足，无需转换")
    
    def allocate_matched_students(self):
        """分配已匹配学生"""
        print("\n=== 分配已匹配学生 ===")

        # 按宿舍组分组
        matched_groups = defaultdict(list)
        for student in self.matched_students:
            dorm_type = student.get('类型', '')
            dorm_num = student.get('宿舍序号', '')
            if dorm_num and str(dorm_num).strip() and str(dorm_num).strip() != 'nan':
                key = f"{dorm_type}_{dorm_num}"
                matched_groups[key].append(student)

        # 获取可用床位，按房间分组
        available_beds = [b for b in self.dormitory_beds if self.get_bed_key(b) not in self.used_beds]
        beds_by_room = defaultdict(list)
        for bed in available_beds:
            room_key = f"{bed['宿舍楼']}_{bed['房间号']}"
            beds_by_room[room_key].append(bed)

        allocated_count = 0
        for group_key, group_students in matched_groups.items():
            if not group_students:
                continue

            gender = group_students[0].get('性别')
            group_size = len(group_students)

            print(f"处理组 {group_key}: {group_size}人 ({gender})")

            # 找到能容纳整个组的房间
            suitable_room = None
            for room_key, room_beds in beds_by_room.items():
                # 检查房间是否有足够的同性别床位
                gender_beds = [b for b in room_beds if b['性别'] == gender and self.get_bed_key(b) not in self.used_beds]
                if len(gender_beds) >= group_size:
                    suitable_room = room_key
                    break

            if suitable_room:
                # 分配到同一房间
                room_beds = [b for b in beds_by_room[suitable_room]
                           if b['性别'] == gender and self.get_bed_key(b) not in self.used_beds]

                for i, student_data in enumerate(group_students):
                    if i >= len(room_beds):
                        break

                    # 已匹配学生数据中没有学号，需要根据姓名在法学院名单中查找
                    student_data_name = student_data.get('姓名', '')

                    # 根据姓名在法学院名单中查找对应的学生（获取学号）
                    student = self.get_student_by_name(student_data_name)
                    if student and student['学号'] not in self.allocated_students:
                        bed = room_beds[i]
                        if self.allocate_bed_to_student(student, bed, '已匹配'):
                            allocated_count += 1
                            # 从可用床位中移除
                            beds_by_room[suitable_room].remove(bed)

                print(f"  ✓ 组 {group_key} 分配到房间 {suitable_room}")
            else:
                # 如果找不到能容纳整个组的房间，分散分配
                print(f"  ⚠️  组 {group_key} 无法分配到同一房间，进行分散分配")

                # 获取所有可用的同性别床位
                gender_beds = [b for b in available_beds
                             if b['性别'] == gender and self.get_bed_key(b) not in self.used_beds]

                for i, student_data in enumerate(group_students):
                    if i >= len(gender_beds):
                        break

                    student_data_name = student_data.get('姓名', '')
                    student = self.get_student_by_name(student_data_name)
                    if student and student['学号'] not in self.allocated_students:
                        bed = gender_beds[i]
                        if self.allocate_bed_to_student(student, bed, '已匹配'):
                            allocated_count += 1

        print(f"✓ 已匹配学生分配完成: {allocated_count} 人")
    
    def allocate_remaining_students(self):
        """分配剩余学生"""
        print("\n=== 分配剩余学生 ===")
        
        # 获取需要分配的学生
        # 注意：排除名单中的学生不在法学院名单中，所以不需要检查排除名单
        remaining_students = []
        for student in self.students:
            if student['学号'] not in self.allocated_students:
                remaining_students.append(student)
        
        male_remaining = [s for s in remaining_students if s['性别'] == '男']
        female_remaining = [s for s in remaining_students if s['性别'] == '女']
        
        print(f"需要分配: 男生 {len(male_remaining)}, 女生 {len(female_remaining)}")
        
        # 获取剩余床位
        available_beds = [b for b in self.dormitory_beds if self.get_bed_key(b) not in self.used_beds]
        male_beds = [b for b in available_beds if b['性别'] == '男']
        female_beds = [b for b in available_beds if b['性别'] == '女']
        
        print(f"剩余床位: 男生 {len(male_beds)}, 女生 {len(female_beds)}")
        
        # 随机分配
        random.seed(42)
        random.shuffle(male_remaining)
        random.shuffle(female_remaining)
        random.shuffle(male_beds)
        random.shuffle(female_beds)
        
        # 分配男生
        male_allocated = 0
        for i, student in enumerate(male_remaining):
            if i < len(male_beds):
                bed = male_beds[i]
                if self.allocate_bed_to_student(student, bed, '随机分配'):
                    male_allocated += 1
        
        # 分配女生
        female_allocated = 0
        for i, student in enumerate(female_remaining):
            if i < len(female_beds):
                bed = female_beds[i]
                if self.allocate_bed_to_student(student, bed, '随机分配'):
                    female_allocated += 1
        
        print(f"✓ 随机分配完成: 男生 {male_allocated}, 女生 {female_allocated}")
    
    def validate_allocation(self):
        """验证分配结果"""
        print("\n=== 验证分配结果 ===")
        
        # 检查床位唯一性
        bed_keys = []
        for result in self.allocation_result:
            bed_key = f"{result['宿舍楼']}_{result['房间号']}_{result['床位号']}"
            bed_keys.append(bed_key)
        
        unique_beds = set(bed_keys)
        if len(bed_keys) == len(unique_beds):
            print("✅ 床位唯一性检查通过")
        else:
            print(f"❌ 床位重复分配: {len(bed_keys) - len(unique_beds)} 个重复")
        
        # 检查学生唯一性
        student_ids = [result['学号'] for result in self.allocation_result]
        unique_students = set(student_ids)
        if len(student_ids) == len(unique_students):
            print("✅ 学生唯一性检查通过")
        else:
            print(f"❌ 学生重复分配: {len(student_ids) - len(unique_students)} 个重复")
        
        return len(bed_keys) == len(unique_beds) and len(student_ids) == len(unique_students)
    
    def generate_report(self):
        """生成报告"""
        print("\n=== 生成分配报告 ===")
        
        # 保存分配结果
        with open("修复分配结果.csv", 'w', encoding='utf-8-sig', newline='') as f:
            fieldnames = ['学号', '姓名', '性别', '专业', '宿舍楼', '房间号', '床位号', '分配类型']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.allocation_result)
        
        # 统计信息
        total = len(self.allocation_result)
        male_count = len([r for r in self.allocation_result if r['性别'] == '男'])
        female_count = len([r for r in self.allocation_result if r['性别'] == '女'])
        matched_count = len([r for r in self.allocation_result if r['分配类型'] == '已匹配'])
        random_count = len([r for r in self.allocation_result if r['分配类型'] == '随机分配'])
        
        remaining_beds = len(self.dormitory_beds) - len(self.used_beds)
        
        report = f"""=== 修复版宿舍分配报告 ===

分配总结:
- 总分配人数: {total}
- 男生: {male_count} 人
- 女生: {female_count} 人

按分配方式:
- 已匹配分配: {matched_count} 人
- 随机分配: {random_count} 人

床位使用:
- 已使用床位: {len(self.used_beds)} 个
- 剩余床位: {remaining_beds} 个

数据完整性:
- 床位唯一性: ✅
- 学生唯一性: ✅

生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        print(report)
        
        with open("修复分配报告.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✓ 报告已保存")
    
    def run(self):
        """运行修复的分配流程"""
        print("=== 修复版宿舍分配系统 ===\n")
        
        random.seed(42)
        
        if not self.load_data():
            return False
        
        self.resolve_gender_conflicts()
        self.allocate_matched_students()
        self.allocate_remaining_students()
        
        if self.validate_allocation():
            print("✅ 分配验证通过")
        else:
            print("❌ 分配验证失败")
        
        self.generate_report()
        
        print("\n=== 分配完成 ===")
        return True

if __name__ == "__main__":
    import os
    allocator = FixedDormitoryAllocator()
    allocator.run()
