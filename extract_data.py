#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理脚本
转换CSV文件编码为UTF-8格式
"""

import pandas as pd
import os
import sys
import csv

def convert_csv_encoding():
    """转换CSV文件编码为UTF-8"""
    print("=== 转换CSV文件编码 ===")

    files_to_convert = [
        "需排除人员名单.csv",
        "已匹配的宿舍.csv",
        "宿舍情况.csv"
    ]

    for filename in files_to_convert:
        if os.path.exists(filename):
            print(f"处理文件: {filename}")
            try:
                # 尝试不同编码读取
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None

                for encoding in encodings:
                    try:
                        df = pd.read_csv(filename, encoding=encoding)
                        print(f"  成功使用编码 {encoding} 读取")
                        break
                    except:
                        continue

                if df is not None:
                    # 重新保存为UTF-8编码
                    output_file = f"{filename.replace('.csv', '_utf8.csv')}"
                    df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"  已转换并保存到: {output_file}")
                else:
                    print(f"  无法读取文件 {filename}")

            except Exception as e:
                print(f"  处理文件 {filename} 时出错: {e}")
        else:
            print(f"文件不存在: {filename}")

    return True

def check_existing_files():
    """检查现有CSV文件的编码和内容"""
    files_to_check = [
        "需排除人员名单.csv",
        "已匹配的宿舍.csv", 
        "宿舍情况.csv"
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"\n检查文件: {filename}")
            try:
                # 尝试不同编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None
                
                for encoding in encodings:
                    try:
                        df = pd.read_csv(filename, encoding=encoding)
                        print(f"成功使用编码 {encoding} 读取文件")
                        break
                    except:
                        continue
                
                if df is not None:
                    print(f"文件形状: {df.shape}")
                    print(f"列名: {df.columns.tolist()}")
                    print("前3行数据:")
                    print(df.head(3))
                    
                    # 重新保存为UTF-8编码
                    output_file = f"{filename.replace('.csv', '_utf8.csv')}"
                    df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"已转换编码并保存到: {output_file}")
                else:
                    print(f"无法读取文件 {filename}")
                    
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")

if __name__ == "__main__":
    print("开始数据预处理...")

    # 转换CSV文件编码
    convert_csv_encoding()

    # 检查现有CSV文件
    check_existing_files()

    print("\n数据预处理完成！")
