#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复分配结果的唯一性
"""

import csv
from collections import Counter

def verify_fixed_allocation():
    """验证修复分配结果"""
    print("=== 验证修复分配结果 ===")
    
    # 读取修复分配结果
    results = []
    with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            results.append(row)
    
    print(f"分配结果总数: {len(results)}")
    
    # 检查床位唯一性
    bed_keys = []
    for result in results:
        bed_key = f"{result['宿舍楼']}_{result['房间号']}_{result['床位号']}"
        bed_keys.append(bed_key)
    
    bed_counts = Counter(bed_keys)
    duplicate_beds = [(key, count) for key, count in bed_counts.items() if count > 1]
    
    if duplicate_beds:
        print(f"❌ 发现 {len(duplicate_beds)} 个重复床位:")
        for key, count in duplicate_beds[:5]:
            print(f"  {key}: {count} 次")
    else:
        print("✅ 床位唯一性检查通过")
    
    # 检查学生唯一性
    student_ids = [result['学号'] for result in results]
    id_counts = Counter(student_ids)
    duplicate_ids = [(sid, count) for sid, count in id_counts.items() if count > 1]
    
    if duplicate_ids:
        print(f"❌ 发现 {len(duplicate_ids)} 个重复学号:")
        for sid, count in duplicate_ids[:5]:
            print(f"  {sid}: {count} 次")
    else:
        print("✅ 学生唯一性检查通过")
    
    # 统计信息
    male_count = len([r for r in results if r['性别'] == '男'])
    female_count = len([r for r in results if r['性别'] == '女'])
    matched_count = len([r for r in results if r['分配类型'] == '已匹配'])
    random_count = len([r for r in results if r['分配类型'] == '随机分配'])
    
    print(f"\n统计信息:")
    print(f"总分配: {len(results)} 人")
    print(f"男生: {male_count} 人")
    print(f"女生: {female_count} 人")
    print(f"已匹配: {matched_count} 人")
    print(f"随机分配: {random_count} 人")
    
    # 验证结果
    is_valid = len(duplicate_beds) == 0 and len(duplicate_ids) == 0
    
    if is_valid:
        print(f"\n🎉 修复分配结果验证通过！")
        print(f"✅ 床位和学生都保证唯一性")
        print(f"✅ 成功分配 {len(results)} 名学生")
    else:
        print(f"\n❌ 修复分配结果验证失败")
    
    return is_valid

if __name__ == "__main__":
    verify_fixed_allocation()
