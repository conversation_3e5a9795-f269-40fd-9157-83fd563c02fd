#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试宿舍分配系统
"""

import pandas as pd
import os

def test_data_loading():
    """测试数据加载"""
    print("=== 测试数据加载 ===")
    
    # 测试学生数据
    student_files = [
        "法学院人员名单_法学女生.csv",
        "法学院人员名单_法学男生.csv", 
        "法学院人员名单_法外女生.csv",
        "法学院人员名单_法外男生.csv"
    ]
    
    all_students = []
    for file in student_files:
        if os.path.exists(file):
            print(f"加载文件: {file}")
            df = pd.read_csv(file, encoding='utf-8-sig')
            print(f"  形状: {df.shape}")
            print(f"  列名: {df.columns.tolist()}")
            
            # 添加专业信息
            if "法学" in file:
                df['专业'] = '法学'
            else:
                df['专业'] = '法外'
            
            # 清理数据
            df_clean = df.dropna(subset=['姓名'])
            print(f"  清理后: {len(df_clean)} 行")
            all_students.append(df_clean)
        else:
            print(f"文件不存在: {file}")
    
    if all_students:
        combined = pd.concat(all_students, ignore_index=True)
        print(f"合并后总学生数: {len(combined)}")
        print(f"性别分布: {combined['性别'].value_counts().to_dict()}")
        print(f"专业分布: {combined['专业'].value_counts().to_dict()}")
    
    # 测试其他文件
    other_files = [
        "需排除人员名单_utf8.csv",
        "已匹配的宿舍_utf8.csv",
        "宿舍情况_utf8.csv"
    ]
    
    for file in other_files:
        if os.path.exists(file):
            print(f"\n加载文件: {file}")
            df = pd.read_csv(file, encoding='utf-8-sig')
            print(f"  形状: {df.shape}")
            print(f"  列名: {df.columns.tolist()}")
            if '性别' in df.columns:
                print(f"  性别分布: {df['性别'].value_counts().to_dict()}")
        else:
            print(f"文件不存在: {file}")

def test_data_validation():
    """测试数据验证"""
    print("\n=== 测试数据验证 ===")
    
    # 检查需排除人员的唯一性
    if os.path.exists("需排除人员名单_utf8.csv"):
        exclude_df = pd.read_csv("需排除人员名单_utf8.csv", encoding='utf-8-sig')
        duplicates = exclude_df[exclude_df.duplicated(subset=['姓名'], keep=False)]
        if not duplicates.empty:
            print(f"需排除人员中有重复姓名: {duplicates['姓名'].tolist()}")
        else:
            print("需排除人员姓名唯一性检查通过")
    
    # 检查宿舍床位统计
    if os.path.exists("宿舍情况_utf8.csv"):
        dorm_df = pd.read_csv("宿舍情况_utf8.csv", encoding='utf-8-sig')
        print(f"宿舍床位统计:")
        print(f"  总床位: {len(dorm_df)}")
        print(f"  男生床位: {len(dorm_df[dorm_df['性别'] == '男'])}")
        print(f"  女生床位: {len(dorm_df[dorm_df['性别'] == '女'])}")
        print(f"  空置床位: {len(dorm_df[dorm_df['使用情况'] == '空置'])}")

def test_simple_allocation():
    """测试简单分配逻辑"""
    print("\n=== 测试简单分配逻辑 ===")
    
    try:
        # 加载数据
        students_df = pd.read_csv("法学院人员名单_法学女生.csv", encoding='utf-8-sig')
        students_df = students_df.dropna(subset=['姓名']).head(5)  # 只取前5个学生测试
        
        dorm_df = pd.read_csv("宿舍情况_utf8.csv", encoding='utf-8-sig')
        female_beds = dorm_df[dorm_df['性别'] == '女'].head(5)  # 只取前5个女生床位
        
        print(f"测试学生: {len(students_df)} 人")
        print(f"测试床位: {len(female_beds)} 个")
        
        # 简单分配
        allocation_result = []
        for i, (_, student) in enumerate(students_df.iterrows()):
            if i < len(female_beds):
                bed = female_beds.iloc[i]
                allocation_result.append({
                    '姓名': student['姓名'],
                    '性别': student['性别'],
                    '宿舍楼': bed['宿舍楼'],
                    '房间号': bed['房间号'],
                    '床位号': bed['床位号']
                })
        
        result_df = pd.DataFrame(allocation_result)
        print(f"分配结果:")
        print(result_df.to_string(index=False))
        
        # 保存测试结果
        result_df.to_csv("测试分配结果.csv", index=False, encoding='utf-8-sig')
        print("测试结果已保存到: 测试分配结果.csv")
        
    except Exception as e:
        print(f"测试分配时出错: {e}")

if __name__ == "__main__":
    test_data_loading()
    test_data_validation()
    test_simple_allocation()
    print("\n测试完成！")
