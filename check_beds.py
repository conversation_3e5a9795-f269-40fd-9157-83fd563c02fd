#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查床位总数和已匹配学生分配情况
"""

import csv
import os
from collections import defaultdict

def check_beds_and_matched():
    print("=== 检查床位总数 ===")
    
    # 检查宿舍情况文件
    dorm_file = "宿舍情况_utf8.csv" if os.path.exists("宿舍情况_utf8.csv") else "宿舍情况.csv"
    
    all_beds = []
    available_beds = []
    
    with open(dorm_file, 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # 所有有床位号的记录
            if (row.get('床位号') and 
                str(row.get('床位号')).strip() and 
                str(row.get('床位号')).strip() != 'nan'):
                all_beds.append(row)
                
                # 空置的床位
                if row.get('使用情况') == '空置':
                    available_beds.append(row)
    
    print(f"总床位数: {len(all_beds)}")
    print(f"空置床位数: {len(available_beds)}")
    
    # 按使用情况统计
    usage_stats = defaultdict(int)
    for bed in all_beds:
        usage = bed.get('使用情况', '未知')
        usage_stats[usage] += 1
    
    print("\n床位使用情况统计:")
    for usage, count in usage_stats.items():
        print(f"  {usage}: {count}")
    
    # 按性别统计空置床位
    male_available = len([b for b in available_beds if b['性别'] == '男'])
    female_available = len([b for b in available_beds if b['性别'] == '女'])
    
    print(f"\n空置床位按性别:")
    print(f"  男生: {male_available}")
    print(f"  女生: {female_available}")
    
    print("\n=== 检查已匹配学生分配情况 ===")
    
    # 加载已匹配学生
    matched_file = "已匹配的宿舍_utf8.csv" if os.path.exists("已匹配的宿舍_utf8.csv") else "已匹配的宿舍.csv"
    matched_students = []
    
    with open(matched_file, 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            matched_students.append(row)
    
    print(f"已匹配学生总数: {len(matched_students)}")
    
    # 按宿舍组分组
    matched_groups = defaultdict(list)
    for student in matched_students:
        dorm_type = student.get('类型', '')
        dorm_num = student.get('宿舍序号', '')
        if dorm_num and str(dorm_num).strip() and str(dorm_num).strip() != 'nan':
            key = f"{dorm_type}_{dorm_num}"
            matched_groups[key].append(student)
    
    print(f"已匹配宿舍组数: {len(matched_groups)}")
    
    # 检查分配结果
    allocated_students = {}
    try:
        with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                if name:
                    allocated_students[name] = row
    except FileNotFoundError:
        print("⚠️  分配结果文件不存在")
        return
    
    print(f"已分配学生总数: {len(allocated_students)}")
    
    # 检查特定学生
    target_students = ['郭臻杰', '魏家宇', '姚怡冰', '支茂森']
    print(f"\n检查特定学生分配情况:")
    
    for name in target_students:
        if name in allocated_students:
            student = allocated_students[name]
            print(f"  {name}: {student['宿舍楼']} {student['房间号']} {student['床位号']} ({student['分配类型']})")
        else:
            print(f"  {name}: 未分配")
    
    # 检查每个已匹配组的分配情况
    print(f"\n检查已匹配组分配情况:")
    
    for group_key, group_students in matched_groups.items():
        print(f"\n组 {group_key} ({len(group_students)}人):")
        
        allocated_rooms = set()
        for student_data in group_students:
            name = student_data.get('姓名', '')
            if name in allocated_students:
                allocated = allocated_students[name]
                room_key = f"{allocated['宿舍楼']}_{allocated['房间号']}"
                allocated_rooms.add(room_key)
                print(f"  {name}: {allocated['宿舍楼']} {allocated['房间号']} {allocated['床位号']}")
            else:
                print(f"  {name}: 未分配")
        
        if len(allocated_rooms) > 1:
            print(f"  ⚠️  该组学生被分配到了不同房间: {allocated_rooms}")
        elif len(allocated_rooms) == 1:
            print(f"  ✅ 该组学生都在同一房间: {list(allocated_rooms)[0]}")
        else:
            print(f"  ❌ 该组没有学生被分配")

if __name__ == "__main__":
    check_beds_and_matched()
