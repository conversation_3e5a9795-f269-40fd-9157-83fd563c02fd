#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版宿舍分配系统
直接从现有的法学院人员名单.csv读取数据
"""

import csv
import random
import os
from collections import defaultdict

class SimplifiedDormitoryAllocator:
    def __init__(self):
        self.students = []
        self.exclude_names = set()
        self.matched_students = []
        self.dormitory_beds = []
        self.allocation_result = []
        
    def load_data(self):
        """加载所有数据"""
        print("=== 加载数据 ===")
        
        # 1. 加载学生名单
        if not os.path.exists("法学院人员名单.csv"):
            print("错误: 找不到法学院人员名单.csv文件")
            return False
            
        with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                gender = row.get('性别', '').strip()
                major = row.get('专业', '').strip()
                student_id = row.get('学号', '').strip()
                if name and gender:
                    self.students.append({
                        '学号': student_id,
                        '姓名': name,
                        '性别': gender,
                        '专业': major
                    })
        
        print(f"✓ 加载学生: {len(self.students)} 人")
        
        # 2. 加载需排除学生
        exclude_file = "需排除人员名单_utf8.csv" if os.path.exists("需排除人员名单_utf8.csv") else "需排除人员名单.csv"
        if os.path.exists(exclude_file):
            with open(exclude_file, 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    name = row.get('姓名', '').strip()
                    if name:
                        self.exclude_names.add(name)
            print(f"✓ 需排除学生: {len(self.exclude_names)} 人")
        
        # 3. 加载已匹配学生
        matched_file = "已匹配的宿舍_utf8.csv" if os.path.exists("已匹配的宿舍_utf8.csv") else "已匹配的宿舍.csv"
        if os.path.exists(matched_file):
            with open(matched_file, 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    self.matched_students.append(row)
            print(f"✓ 已匹配学生: {len(self.matched_students)} 人")
        
        # 4. 加载宿舍信息
        dorm_file = "宿舍情况_utf8.csv" if os.path.exists("宿舍情况_utf8.csv") else "宿舍情况.csv"
        if os.path.exists(dorm_file):
            with open(dorm_file, 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    self.dormitory_beds.append(row)
            print(f"✓ 宿舍床位: {len(self.dormitory_beds)} 个")
        
        return True
    
    def analyze_capacity(self):
        """分析容量需求"""
        print("\n=== 容量分析 ===")
        
        # 统计床位
        male_beds = len([b for b in self.dormitory_beds if b.get('性别') == '男' and b.get('使用情况') == '空置'])
        female_beds = len([b for b in self.dormitory_beds if b.get('性别') == '女' and b.get('使用情况') == '空置'])
        
        # 统计学生需求
        all_male = len([s for s in self.students if s.get('性别') == '男'])
        all_female = len([s for s in self.students if s.get('性别') == '女'])
        
        # 计算排除后的需求
        exclude_male = 0
        exclude_female = 0
        for name in self.exclude_names:
            for student in self.students:
                if student['姓名'] == name:
                    if student['性别'] == '男':
                        exclude_male += 1
                    else:
                        exclude_female += 1
                    break
        
        need_male = all_male - exclude_male
        need_female = all_female - exclude_female
        
        matched_male = len([s for s in self.matched_students if s.get('性别') == '男'])
        matched_female = len([s for s in self.matched_students if s.get('性别') == '女'])
        
        print(f"床位供给: 男生 {male_beds}, 女生 {female_beds}")
        print(f"学生需求: 男生 {need_male}, 女生 {need_female}")
        print(f"已匹配: 男生 {matched_male}, 女生 {matched_female}")
        print(f"需随机分配: 男生 {need_male - matched_male}, 女生 {need_female - matched_female}")
        
        # 检查容量
        if male_beds < need_male:
            print(f"⚠️  警告: 男生床位不足 {need_male - male_beds} 个")
        if female_beds < need_female:
            print(f"⚠️  警告: 女生床位不足 {need_female - female_beds} 个")
        
        return {
            'male_beds': male_beds,
            'female_beds': female_beds,
            'need_male': need_male,
            'need_female': need_female,
            'matched_male': matched_male,
            'matched_female': matched_female
        }
    
    def allocate_matched_students(self):
        """分配已匹配学生"""
        print("\n=== 分配已匹配学生 ===")
        
        # 按宿舍组分组
        matched_groups = defaultdict(list)
        for student in self.matched_students:
            dorm_type = student.get('类型', '')
            dorm_num = student.get('宿舍序号', '')
            if dorm_num and str(dorm_num).strip() and str(dorm_num).strip() != 'nan':
                key = f"{dorm_type}_{dorm_num}"
                matched_groups[key].append(student)
        
        print(f"已匹配宿舍组数: {len(matched_groups)}")
        
        # 获取可用床位
        available_beds = [b for b in self.dormitory_beds if b.get('使用情况') == '空置']
        male_beds = [b for b in available_beds if b.get('性别') == '男']
        female_beds = [b for b in available_beds if b.get('性别') == '女']
        
        used_bed_indices = {'male': set(), 'female': set()}
        
        for group_key, group_students in matched_groups.items():
            if not group_students:
                continue
                
            gender = group_students[0].get('性别')
            group_size = len(group_students)
            
            print(f"  分配组 {group_key}: {group_size} 名{gender}生")
            
            # 选择床位
            target_beds = male_beds if gender == '男' else female_beds
            bed_type = 'male' if gender == '男' else 'female'
            selected_beds = []
            
            for i, bed in enumerate(target_beds):
                if i not in used_bed_indices[bed_type]:
                    selected_beds.append((i, bed))
                    used_bed_indices[bed_type].add(i)
                    if len(selected_beds) >= group_size:
                        break
            
            # 分配床位
            for i, student in enumerate(group_students):
                if i < len(selected_beds):
                    _, bed = selected_beds[i]
                    self.allocation_result.append({
                        '学号': self._get_student_id(student.get('姓名', '')),
                        '姓名': student.get('姓名', ''),
                        '性别': student.get('性别', ''),
                        '专业': self._get_student_major(student.get('姓名', '')),
                        '宿舍楼': bed.get('宿舍楼', ''),
                        '房间号': bed.get('房间号', ''),
                        '床位号': bed.get('床位号', ''),
                        '分配类型': '已匹配'
                    })
                    bed['使用情况'] = '已分配'
        
        matched_count = len([r for r in self.allocation_result if r['分配类型'] == '已匹配'])
        print(f"✓ 已匹配学生分配完成: {matched_count} 人")
    
    def allocate_remaining_students(self):
        """分配剩余学生"""
        print("\n=== 分配剩余学生 ===")
        
        # 获取需要分配的学生
        matched_names = set(s.get('姓名', '') for s in self.matched_students)
        
        remaining_students = []
        for student in self.students:
            name = student['姓名']
            if name not in self.exclude_names and name not in matched_names:
                remaining_students.append(student)
        
        male_remaining = [s for s in remaining_students if s['性别'] == '男']
        female_remaining = [s for s in remaining_students if s['性别'] == '女']
        
        print(f"需要分配: 男生 {len(male_remaining)}, 女生 {len(female_remaining)}")
        
        # 获取剩余床位
        available_beds = [b for b in self.dormitory_beds if b.get('使用情况') == '空置']
        male_beds = [b for b in available_beds if b.get('性别') == '男']
        female_beds = [b for b in available_beds if b.get('性别') == '女']
        
        print(f"剩余床位: 男生 {len(male_beds)}, 女生 {len(female_beds)}")
        
        # 随机分配男生
        random.shuffle(male_remaining)
        random.shuffle(male_beds)
        
        for i, student in enumerate(male_remaining):
            if i < len(male_beds):
                bed = male_beds[i]
                self.allocation_result.append({
                    '学号': student['学号'],
                    '姓名': student['姓名'],
                    '性别': student['性别'],
                    '专业': student['专业'],
                    '宿舍楼': bed.get('宿舍楼', ''),
                    '房间号': bed.get('房间号', ''),
                    '床位号': bed.get('床位号', ''),
                    '分配类型': '随机分配'
                })
                bed['使用情况'] = '已分配'
        
        # 随机分配女生
        random.shuffle(female_remaining)
        random.shuffle(female_beds)
        
        for i, student in enumerate(female_remaining):
            if i < len(female_beds):
                bed = female_beds[i]
                self.allocation_result.append({
                    '学号': student['学号'],
                    '姓名': student['姓名'],
                    '性别': student['性别'],
                    '专业': student['专业'],
                    '宿舍楼': bed.get('宿舍楼', ''),
                    '房间号': bed.get('房间号', ''),
                    '床位号': bed.get('床位号', ''),
                    '分配类型': '随机分配'
                })
                bed['使用情况'] = '已分配'
        
        random_count = len([r for r in self.allocation_result if r['分配类型'] == '随机分配'])
        print(f"✓ 随机分配完成: {random_count} 人")
    
    def _get_student_id(self, name):
        """根据姓名获取学号"""
        for student in self.students:
            if student['姓名'] == name:
                return student.get('学号', '')
        return ''
    
    def _get_student_major(self, name):
        """根据姓名获取专业"""
        for student in self.students:
            if student['姓名'] == name:
                return student.get('专业', '')
        return ''
    
    def save_results(self):
        """保存分配结果"""
        print("\n=== 保存结果 ===")
        
        # 保存分配结果
        if self.allocation_result:
            with open("宿舍分配结果.csv", 'w', encoding='utf-8-sig', newline='') as f:
                fieldnames = ['学号', '姓名', '性别', '专业', '宿舍楼', '房间号', '床位号', '分配类型']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.allocation_result)
            
            print(f"✓ 分配结果已保存: 宿舍分配结果.csv ({len(self.allocation_result)} 人)")
        
        # 生成统计报告
        total = len(self.allocation_result)
        male_count = len([r for r in self.allocation_result if r['性别'] == '男'])
        female_count = len([r for r in self.allocation_result if r['性别'] == '女'])
        matched_count = len([r for r in self.allocation_result if r['分配类型'] == '已匹配'])
        random_count = len([r for r in self.allocation_result if r['分配类型'] == '随机分配'])
        
        # 统计专业分布
        law_count = len([r for r in self.allocation_result if r['专业'] == '法学'])
        non_law_count = len([r for r in self.allocation_result if r['专业'] == '法外'])
        
        # 统计剩余床位
        remaining_beds = len([b for b in self.dormitory_beds if b.get('使用情况') == '空置'])
        remaining_male_beds = len([b for b in self.dormitory_beds 
                                  if b.get('使用情况') == '空置' and b.get('性别') == '男'])
        remaining_female_beds = len([b for b in self.dormitory_beds 
                                    if b.get('使用情况') == '空置' and b.get('性别') == '女'])
        
        report = f"""=== 宿舍分配统计报告 ===

分配总结:
- 总分配人数: {total}
- 男生: {male_count} 人
- 女生: {female_count} 人

按专业分布:
- 法学专业: {law_count} 人
- 法外专业: {non_law_count} 人

按分配方式:
- 已匹配分配: {matched_count} 人
- 随机分配: {random_count} 人

剩余床位:
- 总剩余: {remaining_beds} 个
- 男生床位: {remaining_male_beds} 个
- 女生床位: {remaining_female_beds} 个

生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

注意: 如需重新分配，请修改程序中的random.seed()值
"""
        
        print(report)
        
        with open("宿舍分配报告.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✓ 统计报告已保存: 宿舍分配报告.txt")
    
    def run(self):
        """运行完整分配流程"""
        print("=" * 50)
        print("         宿舍分配系统 v2.0")
        print("=" * 50)
        
        # 设置随机种子（可修改以获得不同结果）
        random.seed(42)
        
        try:
            # 加载数据
            if not self.load_data():
                return False
            
            # 分析容量
            self.analyze_capacity()
            
            # 分配已匹配学生
            self.allocate_matched_students()
            
            # 分配剩余学生
            self.allocate_remaining_students()
            
            # 保存结果
            self.save_results()
            
            print("\n" + "=" * 50)
            print("         分配完成！")
            print("=" * 50)
            print("如需重新分配，请修改random.seed()的值后重新运行")
            
            return True
            
        except Exception as e:
            print(f"❌ 分配过程中出错: {e}")
            return False

if __name__ == "__main__":
    allocator = SimplifiedDormitoryAllocator()
    allocator.run()
