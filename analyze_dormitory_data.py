#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析宿舍数据结构
"""

import csv
from collections import defaultdict

def analyze_dormitory_structure():
    """分析宿舍数据结构"""
    print("=== 分析宿舍数据结构 ===")
    
    # 读取宿舍数据
    dormitory_data = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            dormitory_data.append(row)
    
    print(f"总床位数: {len(dormitory_data)}")
    
    # 按宿舍楼分组
    buildings = defaultdict(list)
    for bed in dormitory_data:
        building = bed['宿舍楼']
        buildings[building].append(bed)
    
    print(f"\n宿舍楼数量: {len(buildings)}")
    for building, beds in buildings.items():
        print(f"  {building}: {len(beds)} 个床位")
    
    # 按房间分组
    rooms = defaultdict(list)
    for bed in dormitory_data:
        room_key = f"{bed['宿舍楼']}-{bed['房间号']}"
        rooms[room_key].append(bed)
    
    print(f"\n房间数量: {len(rooms)}")
    
    # 分析房间容量
    room_capacities = defaultdict(int)
    for room_key, beds in rooms.items():
        capacity = len(beds)
        room_capacities[capacity] += 1
        if capacity != 4:  # 标准宿舍应该是4人间
            print(f"  非标准容量房间 {room_key}: {capacity} 个床位")
    
    print(f"\n房间容量分布:")
    for capacity, count in sorted(room_capacities.items()):
        print(f"  {capacity}人间: {count} 个房间")
    
    # 按性别统计
    gender_stats = defaultdict(int)
    for bed in dormitory_data:
        gender_stats[bed['性别']] += 1
    
    print(f"\n按性别统计床位:")
    for gender, count in gender_stats.items():
        print(f"  {gender}: {count} 个床位")
    
    # 按性别和宿舍楼统计
    print(f"\n按性别和宿舍楼统计:")
    for building, beds in buildings.items():
        male_beds = len([b for b in beds if b['性别'] == '男'])
        female_beds = len([b for b in beds if b['性别'] == '女'])
        print(f"  {building}:")
        print(f"    男生床位: {male_beds}")
        print(f"    女生床位: {female_beds}")
    
    # 分析房间性别分布
    print(f"\n房间性别分布检查:")
    mixed_rooms = []
    for room_key, beds in rooms.items():
        genders = set(bed['性别'] for bed in beds)
        if len(genders) > 1:
            mixed_rooms.append(room_key)
    
    if mixed_rooms:
        print(f"  发现 {len(mixed_rooms)} 个混合性别房间:")
        for room in mixed_rooms[:5]:  # 只显示前5个
            print(f"    {room}")
    else:
        print("  所有房间都是单一性别")
    
    return dormitory_data, rooms

def analyze_student_data():
    """分析学生数据"""
    print(f"\n=== 分析学生数据 ===")
    
    # 统计所有学生
    all_students = []
    student_files = [
        ("法学院人员名单_法学女生.csv", "法学"),
        ("法学院人员名单_法学男生.csv", "法学"),
        ("法学院人员名单_法外女生.csv", "法外"),
        ("法学院人员名单_法外男生.csv", "法外")
    ]
    
    for filename, major in student_files:
        with open(filename, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row.get('姓名') and row.get('姓名').strip():
                    row['专业'] = major
                    all_students.append(row)
    
    print(f"总学生数: {len(all_students)}")
    
    # 按性别统计
    male_students = len([s for s in all_students if s.get('性别') == '男'])
    female_students = len([s for s in all_students if s.get('性别') == '女'])
    print(f"男学生: {male_students}")
    print(f"女学生: {female_students}")
    
    # 按专业统计
    law_students = len([s for s in all_students if s.get('专业') == '法学'])
    non_law_students = len([s for s in all_students if s.get('专业') == '法外'])
    print(f"法学专业: {law_students}")
    print(f"法外专业: {non_law_students}")
    
    # 分析需排除学生
    exclude_students = []
    with open("需排除人员名单_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            exclude_students.append(row)
    
    print(f"\n需排除学生: {len(exclude_students)}")
    exclude_male = len([s for s in exclude_students if s.get('性别') == '男'])
    exclude_female = len([s for s in exclude_students if s.get('性别') == '女'])
    print(f"  男生: {exclude_male}")
    print(f"  女生: {exclude_female}")
    
    # 分析已匹配学生
    matched_students = []
    with open("已匹配的宿舍_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            matched_students.append(row)
    
    print(f"\n已匹配学生: {len(matched_students)}")
    matched_male = len([s for s in matched_students if s.get('性别') == '男'])
    matched_female = len([s for s in matched_students if s.get('性别') == '女'])
    print(f"  男生: {matched_male}")
    print(f"  女生: {matched_female}")
    
    # 计算需要随机分配的学生数
    exclude_names = set(s['姓名'] for s in exclude_students)
    matched_names = set(s['姓名'] for s in matched_students)
    
    remaining_students = []
    for student in all_students:
        name = student.get('姓名', '').strip()
        if name and name not in exclude_names and name not in matched_names:
            remaining_students.append(student)
    
    print(f"\n需要随机分配的学生: {len(remaining_students)}")
    remaining_male = len([s for s in remaining_students if s.get('性别') == '男'])
    remaining_female = len([s for s in remaining_students if s.get('性别') == '女'])
    print(f"  男生: {remaining_male}")
    print(f"  女生: {remaining_female}")
    
    return all_students, exclude_students, matched_students, remaining_students

def calculate_capacity():
    """计算实际容量需求"""
    print(f"\n=== 容量需求分析 ===")
    
    dormitory_data, rooms = analyze_dormitory_structure()
    all_students, exclude_students, matched_students, remaining_students = analyze_student_data()
    
    # 计算床位容量
    male_beds = len([b for b in dormitory_data if b['性别'] == '男'])
    female_beds = len([b for b in dormitory_data if b['性别'] == '女'])
    
    # 计算学生需求
    remaining_male = len([s for s in remaining_students if s.get('性别') == '男'])
    remaining_female = len([s for s in remaining_students if s.get('性别') == '女'])
    
    matched_male = len([s for s in matched_students if s.get('性别') == '男'])
    matched_female = len([s for s in matched_students if s.get('性别') == '女'])
    
    total_male_need = remaining_male + matched_male
    total_female_need = remaining_female + matched_female
    
    print(f"床位供给:")
    print(f"  男生床位: {male_beds}")
    print(f"  女生床位: {female_beds}")
    print(f"  总床位: {male_beds + female_beds}")
    
    print(f"\n学生需求:")
    print(f"  男生需求: {total_male_need} (已匹配: {matched_male}, 待分配: {remaining_male})")
    print(f"  女生需求: {total_female_need} (已匹配: {matched_female}, 待分配: {remaining_female})")
    print(f"  总需求: {total_male_need + total_female_need}")
    
    print(f"\n供需对比:")
    print(f"  男生: 床位 {male_beds} vs 需求 {total_male_need} = 余量 {male_beds - total_male_need}")
    print(f"  女生: 床位 {female_beds} vs 需求 {total_female_need} = 余量 {female_beds - total_female_need}")
    
    if male_beds < total_male_need:
        print(f"  警告: 男生床位不足 {total_male_need - male_beds} 个")
    if female_beds < total_female_need:
        print(f"  警告: 女生床位不足 {total_female_need - female_beds} 个")

if __name__ == "__main__":
    calculate_capacity()
