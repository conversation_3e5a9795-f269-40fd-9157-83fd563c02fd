#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的宿舍对调和编辑学生功能
"""

def test_gui_import():
    """测试GUI程序导入和新功能"""
    try:
        print("正在测试GUI程序新功能...")
        
        # 测试导入
        import dormitory_gui
        print("✅ GUI程序导入成功")
        
        # 测试创建GUI实例
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        gui = dormitory_gui.DormitoryGUI(root)
        print("✅ GUI实例创建成功")
        
        # 检查新方法是否存在
        new_methods = [
            'room_swap_dialog',
            'edit_student_dialog', 
            'create_room_management_tab',
            'refresh_room_list',
            'preview_room_swap',
            'execute_room_swap',
            'open_student_edit_form'
        ]
        
        for method_name in new_methods:
            if hasattr(gui, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        root.destroy()
        print("\n🎉 所有新功能测试通过！")
        
        print("\n新增功能说明：")
        print("1. 宿舍对调功能：")
        print("   - 点击'宿舍对调'按钮可以对调两个房间的所有学生")
        print("   - 支持预览对调效果")
        print("   - 自动检测性别冲突")
        
        print("\n2. 编辑学生功能：")
        print("   - 点击'编辑学生'按钮可以修改学生信息")
        print("   - 支持搜索学生")
        print("   - 可以修改基本信息和宿舍分配")
        
        print("\n3. 宿舍管理标签页：")
        print("   - 新增专门的宿舍管理界面")
        print("   - 集成宿舍对调功能")
        print("   - 提供详细的对调预览")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    test_gui_import()
