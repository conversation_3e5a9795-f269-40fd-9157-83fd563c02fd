#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成详细的数据统计报告
"""

import csv
import os
from collections import defaultdict, Counter

def generate_data_report():
    """生成完整的数据统计报告"""
    print("=== 宿舍分配系统数据统计报告 ===\n")
    
    # 1. 基础数据统计
    print("📊 基础数据统计")
    print("-" * 50)
    
    # 法学院学生总数
    students = []
    with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row.get('姓名', '').strip() and row.get('学号', '').strip():
                students.append(row)
    
    print(f"法学院学生总数: {len(students)}")
    
    # 按性别统计
    male_students = [s for s in students if s.get('性别') == '男']
    female_students = [s for s in students if s.get('性别') == '女']
    print(f"男生: {len(male_students)} 人")
    print(f"女生: {len(female_students)} 人")
    
    # 按专业统计
    major_count = Counter(s.get('专业', '') for s in students)
    print(f"\n按专业分布:")
    for major, count in major_count.items():
        if major:
            print(f"  {major}: {count} 人")
    
    # 2. 床位数据统计
    print(f"\n🏠 床位数据统计")
    print("-" * 50)
    
    # 总床位
    beds = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row.get('床位号', '').strip():
                beds.append(row)
    
    print(f"总床位数: {len(beds)}")
    
    # 按性别统计床位
    male_beds = [b for b in beds if b.get('性别') == '男']
    female_beds = [b for b in beds if b.get('性别') == '女']
    print(f"男生床位: {len(male_beds)} 个")
    print(f"女生床位: {len(female_beds)} 个")
    
    # 按宿舍楼统计
    building_count = Counter(b.get('宿舍楼', '') for b in beds)
    print(f"\n按宿舍楼分布:")
    for building, count in building_count.items():
        if building:
            print(f"  {building}: {count} 个床位")
    
    # 3. 分配结果统计
    print(f"\n🎯 分配结果统计")
    print("-" * 50)
    
    # 分配结果
    allocation_result = []
    try:
        with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                allocation_result.append(row)
        
        print(f"已分配学生总数: {len(allocation_result)}")
        
        # 按分配类型统计
        allocation_type_count = Counter(r.get('分配类型', '') for r in allocation_result)
        print(f"\n按分配类型:")
        for alloc_type, count in allocation_type_count.items():
            if alloc_type:
                print(f"  {alloc_type}: {count} 人")
        
        # 按性别统计分配
        allocated_male = [r for r in allocation_result if r.get('性别') == '男']
        allocated_female = [r for r in allocation_result if r.get('性别') == '女']
        print(f"\n按性别分配:")
        print(f"  男生: {len(allocated_male)} 人")
        print(f"  女生: {len(allocated_female)} 人")
        
        # 按宿舍楼统计分配
        allocated_building_count = Counter(r.get('宿舍楼', '') for r in allocation_result)
        print(f"\n按宿舍楼分配:")
        for building, count in allocated_building_count.items():
            if building:
                print(f"  {building}: {count} 人")
        
    except FileNotFoundError:
        print("❌ 未找到分配结果文件")
    
    # 4. 床位利用率分析
    print(f"\n📈 床位利用率分析")
    print("-" * 50)
    
    if allocation_result:
        total_beds = len(beds)
        used_beds = len(allocation_result)
        utilization_rate = (used_beds / total_beds) * 100
        
        print(f"总床位数: {total_beds}")
        print(f"已使用床位: {used_beds}")
        print(f"剩余床位: {total_beds - used_beds}")
        print(f"床位利用率: {utilization_rate:.1f}%")
        
        # 按性别分析利用率
        male_used = len(allocated_male)
        female_used = len(allocated_female)
        male_utilization = (male_used / len(male_beds)) * 100 if male_beds else 0
        female_utilization = (female_used / len(female_beds)) * 100 if female_beds else 0
        
        print(f"\n性别床位利用率:")
        print(f"  男生: {male_used}/{len(male_beds)} ({male_utilization:.1f}%)")
        print(f"  女生: {female_used}/{len(female_beds)} ({female_utilization:.1f}%)")
    
    # 5. 房间分布分析
    print(f"\n🏠 房间分布分析")
    print("-" * 50)
    
    if allocation_result:
        # 按房间统计人数
        room_occupancy = defaultdict(list)
        for result in allocation_result:
            building = result.get('宿舍楼', '')
            room = result.get('房间号', '')
            if building and room:
                room_key = f"{building} - {room}"
                room_occupancy[room_key].append(result)
        
        # 统计房间入住情况
        occupancy_stats = Counter(len(students) for students in room_occupancy.values())
        print(f"房间入住情况:")
        for occupancy, count in sorted(occupancy_stats.items()):
            print(f"  {occupancy}人间: {count} 个房间")
        
        # 显示部分房间详情
        print(f"\n部分房间详情 (前10个):")
        for i, (room_key, students) in enumerate(sorted(room_occupancy.items())[:10]):
            genders = set(s.get('性别', '') for s in students)
            gender_str = '/'.join(genders) if len(genders) > 1 else list(genders)[0] if genders else '未知'
            print(f"  {room_key}: {len(students)}人 ({gender_str})")
    
    # 6. 已匹配学生分析
    print(f"\n👥 已匹配学生分析")
    print("-" * 50)
    
    try:
        matched_students = []
        with open("已匹配的宿舍_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                matched_students.append(row)
        
        print(f"已匹配学生总数: {len(matched_students)}")
        
        # 按组统计
        matched_groups = defaultdict(list)
        for student in matched_students:
            dorm_type = student.get('类型', '')
            dorm_num = student.get('宿舍序号', '')
            if dorm_num and str(dorm_num).strip() and str(dorm_num).strip() != 'nan':
                key = f"{dorm_type}_{dorm_num}"
                matched_groups[key].append(student)
        
        print(f"已匹配组数: {len(matched_groups)}")
        
        # 按类型统计
        type_count = Counter(s.get('类型', '') for s in matched_students)
        print(f"\n按类型分布:")
        for match_type, count in type_count.items():
            if match_type:
                print(f"  {match_type}: {count} 人")
        
    except FileNotFoundError:
        print("❌ 未找到已匹配学生文件")
    
    # 7. 排除学生分析
    print(f"\n🚫 排除学生分析")
    print("-" * 50)
    
    try:
        exclude_students = []
        with open("需排除人员名单_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row.get('姓名', '').strip():
                    exclude_students.append(row)
        
        print(f"排除学生总数: {len(exclude_students)}")
        
        # 按性别统计
        exclude_male = [s for s in exclude_students if s.get('性别') == '男']
        exclude_female = [s for s in exclude_students if s.get('性别') == '女']
        print(f"排除男生: {len(exclude_male)} 人")
        print(f"排除女生: {len(exclude_female)} 人")
        
    except FileNotFoundError:
        print("❌ 未找到排除学生文件")
    
    print(f"\n" + "=" * 60)
    print("📋 报告生成完成")
    print("=" * 60)

if __name__ == "__main__":
    generate_data_report()
