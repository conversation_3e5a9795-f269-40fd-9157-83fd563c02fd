#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析未分配学生
"""

import csv
import os

def analyze_unallocated():
    print("=== 分析未分配学生 ===")
    
    # 加载法学院人员名单
    students = []
    with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            name = row.get('姓名', '').strip()
            gender = row.get('性别', '').strip()
            student_id = row.get('学号', '').strip()
            if name and gender and student_id:
                students.append(row)
    
    print(f"法学院总人数: {len(students)}")
    
    # 加载分配结果
    allocated_students = set()
    with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            student_id = row.get('学号', '').strip()
            if student_id:
                allocated_students.add(student_id)
    
    print(f"已分配人数: {len(allocated_students)}")
    
    # 找出未分配学生
    unallocated_students = []
    for student in students:
        if student['学号'] not in allocated_students:
            unallocated_students.append(student)
    
    print(f"未分配人数: {len(unallocated_students)}")
    
    # 按性别统计
    male_unallocated = [s for s in unallocated_students if s['性别'] == '男']
    female_unallocated = [s for s in unallocated_students if s['性别'] == '女']
    
    print(f"未分配男生: {len(male_unallocated)}")
    print(f"未分配女生: {len(female_unallocated)}")
    
    # 保存未分配学生名单
    with open("未分配学生名单.csv", 'w', encoding='utf-8-sig', newline='') as f:
        fieldnames = ['学号', '姓名', '性别', '专业', '年级', '民族']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(unallocated_students)
    
    print("✓ 未分配学生名单已保存到 '未分配学生名单.csv'")
    
    # 检查排除名单
    exclude_names = set()
    exclude_file = "需排除人员名单_utf8.csv" if os.path.exists("需排除人员名单_utf8.csv") else "需排除人员名单.csv"
    with open(exclude_file, 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            name = row.get('姓名', '').strip()
            if name:
                exclude_names.add(name)
    
    print(f"排除名单人数: {len(exclude_names)}")
    
    # 检查未分配学生是否在排除名单中
    excluded_unallocated = []
    not_excluded_unallocated = []
    
    for student in unallocated_students:
        if student['姓名'] in exclude_names:
            excluded_unallocated.append(student)
        else:
            not_excluded_unallocated.append(student)
    
    print(f"未分配且在排除名单中: {len(excluded_unallocated)}")
    print(f"未分配且不在排除名单中: {len(not_excluded_unallocated)}")
    
    if excluded_unallocated:
        print("\n在排除名单中的未分配学生:")
        for student in excluded_unallocated:
            print(f"  {student['姓名']} ({student['学号']}) - {student['性别']}")
    
    if not_excluded_unallocated:
        print("\n不在排除名单中的未分配学生:")
        for student in not_excluded_unallocated:
            print(f"  {student['姓名']} ({student['学号']}) - {student['性别']}")
    
    # 检查床位情况
    dormitory_beds = []
    dorm_file = "宿舍情况_utf8.csv" if os.path.exists("宿舍情况_utf8.csv") else "宿舍情况.csv"
    with open(dorm_file, 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if (row.get('床位号') and 
                str(row.get('床位号')).strip() and 
                str(row.get('床位号')).strip() != 'nan' and
                row.get('使用情况') == '空置'):
                dormitory_beds.append(row)
    
    male_beds = len([b for b in dormitory_beds if b['性别'] == '男'])
    female_beds = len([b for b in dormitory_beds if b['性别'] == '女'])
    
    print(f"\n可用床位统计:")
    print(f"男生床位: {male_beds}")
    print(f"女生床位: {female_beds}")
    print(f"总床位: {len(dormitory_beds)}")
    
    # 分析床位不足情况
    male_students = len([s for s in students if s['性别'] == '男'])
    female_students = len([s for s in students if s['性别'] == '女'])
    
    print(f"\n学生需求:")
    print(f"男生总数: {male_students}")
    print(f"女生总数: {female_students}")
    
    print(f"\n床位缺口:")
    print(f"男生缺口: {max(0, male_students - male_beds)}")
    print(f"女生缺口: {max(0, female_students - female_beds)}")

if __name__ == "__main__":
    analyze_unallocated()
