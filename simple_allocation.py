#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版宿舍分配系统
使用内置csv模块，避免pandas依赖问题
"""

import csv
import random
import os

class SimpleDormitoryAllocation:
    def __init__(self):
        self.all_students = []
        self.exclude_students = []
        self.matched_students = []
        self.dormitory_info = []
        self.allocation_result = []
        
    def load_csv_file(self, filename, encoding='utf-8-sig'):
        """加载CSV文件"""
        data = []
        try:
            with open(filename, 'r', encoding=encoding, newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(row)
            print(f"成功加载 {filename}: {len(data)} 行")
            return data
        except Exception as e:
            print(f"加载 {filename} 失败: {e}")
            return []
    
    def load_data(self):
        """加载所有数据文件"""
        print("开始加载数据...")
        
        # 加载学生数据
        student_files = [
            ("法学院人员名单_法学女生.csv", "法学"),
            ("法学院人员名单_法学男生.csv", "法学"),
            ("法学院人员名单_法外女生.csv", "法外"),
            ("法学院人员名单_法外男生.csv", "法外")
        ]
        
        for filename, major in student_files:
            if os.path.exists(filename):
                data = self.load_csv_file(filename)
                for row in data:
                    if row.get('姓名') and row.get('姓名').strip():
                        row['专业'] = major
                        self.all_students.append(row)
        
        print(f"总学生数: {len(self.all_students)}")
        
        # 加载需排除人员
        if os.path.exists("需排除人员名单_utf8.csv"):
            self.exclude_students = self.load_csv_file("需排除人员名单_utf8.csv")
        
        # 加载已匹配人员
        if os.path.exists("已匹配的宿舍_utf8.csv"):
            self.matched_students = self.load_csv_file("已匹配的宿舍_utf8.csv")
        
        # 加载宿舍信息
        if os.path.exists("宿舍情况_utf8.csv"):
            self.dormitory_info = self.load_csv_file("宿舍情况_utf8.csv")
        
        return True
    
    def validate_data(self):
        """验证数据"""
        print("验证数据...")
        
        # 检查需排除人员唯一性
        exclude_names = [row['姓名'] for row in self.exclude_students]
        if len(exclude_names) != len(set(exclude_names)):
            print("警告: 需排除人员名单中有重复姓名")
        
        # 统计信息
        male_students = len([s for s in self.all_students if s.get('性别') == '男'])
        female_students = len([s for s in self.all_students if s.get('性别') == '女'])
        print(f"学生统计: 男生 {male_students}, 女生 {female_students}")
        
        male_beds = len([d for d in self.dormitory_info if d.get('性别') == '男'])
        female_beds = len([d for d in self.dormitory_info if d.get('性别') == '女'])
        print(f"床位统计: 男生 {male_beds}, 女生 {female_beds}")
        
        return True
    
    def get_available_students(self):
        """获取可分配学生"""
        exclude_names = set(row['姓名'] for row in self.exclude_students)
        matched_names = set(row['姓名'] for row in self.matched_students)
        
        available = []
        for student in self.all_students:
            name = student.get('姓名', '').strip()
            if name and name not in exclude_names and name not in matched_names:
                available.append(student)
        
        print(f"可分配学生: {len(available)}")
        return available
    
    def allocate_matched_students(self):
        """分配已匹配学生"""
        print("分配已匹配学生...")
        
        # 按宿舍序号分组
        matched_groups = {}
        for student in self.matched_students:
            dorm_num = student.get('宿舍序号', '').strip()
            if dorm_num and dorm_num != 'nan':
                if dorm_num not in matched_groups:
                    matched_groups[dorm_num] = []
                matched_groups[dorm_num].append(student)
        
        # 获取可用床位
        available_beds = [bed for bed in self.dormitory_info if bed.get('使用情况') == '空置']
        used_beds = set()
        
        for dorm_num, group in matched_groups.items():
            print(f"分配宿舍组 {dorm_num}: {len(group)} 人")
            
            if not group:
                continue
                
            gender = group[0].get('性别')
            
            # 为该组找床位
            group_beds = []
            for bed in available_beds:
                if bed.get('性别') == gender and bed not in used_beds:
                    group_beds.append(bed)
                    used_beds.add(bed)
                    if len(group_beds) >= len(group):
                        break
            
            # 分配床位
            for i, student in enumerate(group):
                if i < len(group_beds):
                    bed = group_beds[i]
                    self.allocation_result.append({
                        '姓名': student.get('姓名', ''),
                        '性别': student.get('性别', ''),
                        '专业': student.get('类型', '').replace('女生', '').replace('男生', ''),
                        '宿舍楼': bed.get('宿舍楼', ''),
                        '房间号': bed.get('房间号', ''),
                        '床位号': bed.get('床位号', ''),
                        '分配类型': '已匹配'
                    })
                    bed['使用情况'] = '已分配'
    
    def allocate_remaining_students(self):
        """分配剩余学生"""
        print("分配剩余学生...")
        
        available_students = self.get_available_students()
        available_beds = [bed for bed in self.dormitory_info if bed.get('使用情况') == '空置']
        
        # 按性别分组
        male_students = [s for s in available_students if s.get('性别') == '男']
        female_students = [s for s in available_students if s.get('性别') == '女']
        
        male_beds = [b for b in available_beds if b.get('性别') == '男']
        female_beds = [b for b in available_beds if b.get('性别') == '女']
        
        print(f"男生: {len(male_students)} 人, 床位: {len(male_beds)}")
        print(f"女生: {len(female_students)} 人, 床位: {len(female_beds)}")
        
        # 随机分配男生
        random.shuffle(male_students)
        random.shuffle(male_beds)
        for i, student in enumerate(male_students):
            if i < len(male_beds):
                bed = male_beds[i]
                self.allocation_result.append({
                    '姓名': student.get('姓名', ''),
                    '性别': student.get('性别', ''),
                    '专业': student.get('专业', ''),
                    '宿舍楼': bed.get('宿舍楼', ''),
                    '房间号': bed.get('房间号', ''),
                    '床位号': bed.get('床位号', ''),
                    '分配类型': '随机分配'
                })
                bed['使用情况'] = '已分配'
        
        # 随机分配女生
        random.shuffle(female_students)
        random.shuffle(female_beds)
        for i, student in enumerate(female_students):
            if i < len(female_beds):
                bed = female_beds[i]
                self.allocation_result.append({
                    '姓名': student.get('姓名', ''),
                    '性别': student.get('性别', ''),
                    '专业': student.get('专业', ''),
                    '宿舍楼': bed.get('宿舍楼', ''),
                    '房间号': bed.get('房间号', ''),
                    '床位号': bed.get('床位号', ''),
                    '分配类型': '随机分配'
                })
                bed['使用情况'] = '已分配'
    
    def save_results(self):
        """保存结果"""
        print("保存分配结果...")
        
        # 保存分配结果
        with open("宿舍分配结果.csv", 'w', encoding='utf-8-sig', newline='') as f:
            if self.allocation_result:
                fieldnames = self.allocation_result[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.allocation_result)
        
        print(f"分配结果已保存: {len(self.allocation_result)} 人")
        
        # 生成统计报告
        total = len(self.allocation_result)
        male_count = len([r for r in self.allocation_result if r.get('性别') == '男'])
        female_count = len([r for r in self.allocation_result if r.get('性别') == '女'])
        matched_count = len([r for r in self.allocation_result if r.get('分配类型') == '已匹配'])
        random_count = len([r for r in self.allocation_result if r.get('分配类型') == '随机分配'])
        
        remaining_beds = len([bed for bed in self.dormitory_info if bed.get('使用情况') == '空置'])
        
        report = f"""=== 宿舍分配统计报告 ===

总分配人数: {total}
男生: {male_count} 人
女生: {female_count} 人

分配方式:
已匹配: {matched_count} 人
随机分配: {random_count} 人

剩余床位: {remaining_beds} 个
"""
        
        print(report)
        
        with open("分配报告.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("报告已保存到: 分配报告.txt")
    
    def run(self):
        """运行分配"""
        print("=== 开始宿舍分配 ===")
        
        if not self.load_data():
            return False
        
        if not self.validate_data():
            return False
        
        self.allocate_matched_students()
        self.allocate_remaining_students()
        self.save_results()
        
        print("=== 分配完成 ===")
        return True

if __name__ == "__main__":
    random.seed(42)  # 设置随机种子
    
    allocator = SimpleDormitoryAllocation()
    allocator.run()
