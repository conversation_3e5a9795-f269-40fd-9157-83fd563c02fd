# 宿舍分配系统使用说明

## 项目概述

本项目是一个自动化的宿舍分配系统，能够：
1. 优先分配已匹配的宿舍人员
2. 随机分配剩余人员（考虑性别和专业）
3. 支持人员名单变更后的重复运行
4. 生成详细的分配报告

## 文件结构

### 输入文件（必需）
- `法学院人员名单.csv` - **主学生名单文件**（包含学号、姓名、性别、专业）
- `需排除人员名单.csv` - 需要排除的人员名单
- `已匹配的宿舍.csv` - 已经匹配好的宿舍人员
- `宿舍情况.csv` - 宿舍床位信息

### 程序文件
- `simplified_allocation.py` - **主分配程序**（推荐使用）
- `extract_data.py` - 数据预处理脚本（转换编码）
- `test_system.py` - 系统测试脚本

### 输出文件
- `宿舍分配结果.csv` - 最终分配结果
- `宿舍分配报告.txt` - 统计报告

## 使用步骤

### 1. 数据准备
确保以下文件存在于项目目录中：
- `法学院人员名单.csv` - 包含学号、姓名、性别、专业
- `需排除人员名单.csv` - 需要排除的学生名单
- `已匹配的宿舍.csv` - 已经匹配好的宿舍人员
- `宿舍情况.csv` - 宿舍床位信息

### 2. 数据预处理（可选）
如果CSV文件编码有问题，运行预处理脚本：
```bash
python extract_data.py
```

### 3. 执行分配
运行主分配程序：
```bash
python simplified_allocation.py
```

这将：
- 直接从法学院人员名单.csv加载学生数据
- 自动处理编码问题
- 分析容量需求
- 优先分配已匹配学生
- 随机分配剩余学生
- 生成分配结果和报告

### 4. 查看结果
检查生成的文件：
- `宿舍分配结果.csv` - 包含每个学生的分配信息
- `宿舍分配报告.txt` - 包含统计信息

## 数据格式说明

### 学生数据格式（法学院人员名单.csv）
```csv
学号,姓名,性别,专业
20250001,张睿宸,女,法学
20250002,吴思静,女,法学
```

### 宿舍数据格式
```csv
宿舍楼,房间号,床位号,使用情况,性别
格致书院E座（东海岸）,504,4,空置,女
```

### 分配结果格式
```csv
姓名,性别,专业,宿舍楼,房间号,床位号,分配类型
张睿宸,女,法学,格致书院E座（东海岸）,504,4,已匹配
```

## 重复运行

当人员名单发生变更时：

1. **更新输入文件**：
   - 更新Excel文件或CSV文件
   - 确保文件格式保持一致

2. **重新提取数据**：
   ```bash
   python extract_data.py
   ```

3. **重新分配**：
   ```bash
   python final_dormitory_allocation.py
   ```

4. **获得不同的随机分配结果**：
   - 编辑 `final_dormitory_allocation.py`
   - 修改 `random.seed(42)` 中的数字
   - 重新运行程序

## 分配逻辑

### 1. 数据验证
- 检查需排除人员的唯一性
- 验证已匹配人员是否在学生名单中
- 分析床位容量是否足够

### 2. 已匹配学生分配
- 按宿舍组分组已匹配学生
- 为每组分配连续的床位
- 确保性别匹配

### 3. 剩余学生随机分配
- 排除已排除和已匹配的学生
- 按性别分组
- 随机打乱顺序
- 分配到剩余床位

### 4. 结果生成
- 生成详细的分配结果CSV
- 生成统计报告
- 记录剩余床位信息

## 注意事项

1. **文件编码**：确保所有CSV文件使用UTF-8编码
2. **数据完整性**：检查学生姓名的唯一性和完整性
3. **容量限制**：系统会警告床位不足的情况
4. **性别匹配**：严格按性别分配床位
5. **可重复性**：通过修改随机种子获得不同结果

## 故障排除

### 常见问题

1. **文件编码错误**
   - 确保CSV文件使用UTF-8编码
   - 运行 `extract_data.py` 重新转换编码

2. **Python环境问题**
   - 确保Python 3.x已安装
   - 检查是否有必要的权限

3. **数据格式错误**
   - 检查CSV文件的列名是否正确
   - 确保必要的列（如姓名、性别）不为空

4. **床位不足**
   - 检查宿舍数据是否完整
   - 考虑调整需排除的人员数量

### 联系支持
如遇到其他问题，请检查：
- 输入文件格式是否正确
- 所有必需文件是否存在
- Python环境是否正常

## 版本信息
- 版本：1.0
- 创建日期：2025年
- 支持的Python版本：3.6+
