# 宿舍分配系统 - 最终使用说明

## 系统概述

本系统是一个完整的宿舍分配解决方案，能够：
- 优先分配已匹配的宿舍人员
- 随机分配剩余人员（严格按性别分配）
- 支持人员名单变更后的重复运行
- 生成详细的分配结果和统计报告

## 必需文件

确保以下4个文件存在于项目目录中：

1. **法学院人员名单.csv** - 主学生名单文件
   - 格式：学号,姓名,性别,专业
   - 示例：20250001,张睿宸,女,法学

2. **需排除人员名单.csv** - 需要排除的学生名单
   - 格式：姓名,性别
   - 示例：李明,男

3. **已匹配的宿舍.csv** - 已经匹配好的宿舍人员
   - 格式：姓名,性别,类型,宿舍序号
   - 示例：张三,男,法学男生,1

4. **宿舍情况.csv** - 宿舍床位信息
   - 格式：宿舍楼,房间号,床位号,使用情况,性别
   - 示例：格致书院E座（东海岸）,504,4,空置,女

## 使用方法

### 方法1：一键运行（推荐）
```bash
run_allocation.bat
```

### 方法2：手动运行
```bash
# 1. 数据预处理（如果有编码问题）
python extract_data.py

# 2. 执行分配
python simplified_allocation.py
```

### 方法3：系统测试
```bash
python test_system.py
```

## 输出文件

运行完成后会生成：

1. **宿舍分配结果.csv** - 详细分配结果
   - 包含：学号,姓名,性别,专业,宿舍楼,房间号,床位号,分配类型

2. **宿舍分配报告.txt** - 统计报告
   - 包含：分配总结、专业分布、分配方式、剩余床位等

## 重新分配

### 当人员名单变更时：
1. 更新 `法学院人员名单.csv` 文件
2. 重新运行分配程序

### 获得不同的随机分配结果：
1. 编辑 `simplified_allocation.py` 文件
2. 修改 `random.seed(42)` 中的数字（如改为123）
3. 重新运行程序

## 分配逻辑

1. **数据加载**：读取所有输入文件
2. **容量分析**：检查床位是否足够
3. **已匹配分配**：优先为已匹配学生分配宿舍
4. **随机分配**：为剩余学生随机分配床位
5. **结果生成**：输出分配结果和统计报告

## 注意事项

- ✅ 系统严格按性别分配床位，不会出现混合宿舍
- ✅ 已匹配学生优先分配，确保组队需求
- ✅ 自动处理文件编码问题
- ✅ 提供详细的容量检查和警告
- ✅ 支持可重复运行

## 故障排除

### 常见问题：

1. **找不到文件**
   - 确保所有4个必需文件都在当前目录
   - 检查文件名是否正确

2. **编码错误**
   - 运行 `python extract_data.py` 转换编码
   - 确保CSV文件使用UTF-8编码

3. **床位不足**
   - 检查宿舍数据是否完整
   - 系统会自动警告并显示不足数量

4. **Python环境问题**
   - 确保Python 3.x已安装
   - 检查是否有必要的权限

## 系统特点

- **智能分配**：优先已匹配，随机分配剩余
- **严格约束**：性别匹配，容量检查
- **可重复性**：支持数据更新和重新分配
- **易用性**：一键运行，详细文档
- **质量保证**：完善的验证和错误处理

## 技术支持

如遇到问题，请检查：
1. 输入文件格式是否正确
2. 所有必需文件是否存在
3. Python环境是否正常
4. 查看错误信息并按提示操作

---

**版本**: v2.0  
**状态**: 生产就绪 ✅  
**最后更新**: 2025年8月14日
