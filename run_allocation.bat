@echo off
chcp 65001
echo ===================================
echo      宿舍分配系统
echo ===================================
echo.

echo 检查必需文件...
if not exist "法学院人员名单.csv" (
    echo 错误: 找不到法学院人员名单.csv文件
    echo 请确保该文件存在于当前目录
    pause
    exit /b 1
)

echo.
echo 步骤1: 数据预处理（可选）...
python extract_data.py

echo.
echo 步骤2: 执行宿舍分配...
python simplified_allocation.py
if errorlevel 1 (
    echo 分配执行失败！
    pause
    exit /b 1
)

echo.
echo ===================================
echo      分配完成！
echo ===================================
echo.
echo 请查看以下文件：
echo - 宿舍分配结果.csv
echo - 宿舍分配报告.txt
echo.
pause
