#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单宿舍分配脚本 - 无依赖版本
"""

import csv
import random
import os

def count_beds_by_gender():
    """统计男女床位数"""
    male_beds = 0
    female_beds = 0
    
    try:
        with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row.get('性别') == '男':
                    male_beds += 1
                elif row.get('性别') == '女':
                    female_beds += 1
        
        print(f"床位统计: 男生 {male_beds}, 女生 {female_beds}, 总计 {male_beds + female_beds}")
        return male_beds, female_beds
    except Exception as e:
        print(f"统计床位时出错: {e}")
        return 0, 0

def load_students():
    """加载所有学生数据"""
    students = []
    files = [
        ("法学院人员名单_法学女生.csv", "法学"),
        ("法学院人员名单_法学男生.csv", "法学"),
        ("法学院人员名单_法外女生.csv", "法外"),
        ("法学院人员名单_法外男生.csv", "法外")
    ]
    
    for filename, major in files:
        try:
            with open(filename, 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row.get('姓名') and row.get('姓名').strip():
                        students.append({
                            '姓名': row['姓名'].strip(),
                            '性别': row.get('性别', '').strip(),
                            '专业': major
                        })
        except Exception as e:
            print(f"加载 {filename} 时出错: {e}")
    
    print(f"加载学生总数: {len(students)}")
    return students

def load_exclude_list():
    """加载需排除学生名单"""
    exclude_names = set()
    try:
        with open("需排除人员名单_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                if name:
                    exclude_names.add(name)
        print(f"需排除学生: {len(exclude_names)}")
    except Exception as e:
        print(f"加载排除名单时出错: {e}")
    
    return exclude_names

def load_matched_students():
    """加载已匹配学生"""
    matched_names = set()
    try:
        with open("已匹配的宿舍_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                if name:
                    matched_names.add(name)
        print(f"已匹配学生: {len(matched_names)}")
    except Exception as e:
        print(f"加载已匹配名单时出错: {e}")
    
    return matched_names

def get_available_students():
    """获取可分配学生"""
    all_students = load_students()
    exclude_names = load_exclude_list()
    matched_names = load_matched_students()
    
    available = []
    for student in all_students:
        name = student['姓名']
        if name not in exclude_names and name not in matched_names:
            available.append(student)
    
    male_available = [s for s in available if s['性别'] == '男']
    female_available = [s for s in available if s['性别'] == '女']
    
    print(f"可分配学生: 总计 {len(available)}, 男生 {len(male_available)}, 女生 {len(female_available)}")
    return available, male_available, female_available

def simple_allocation():
    """执行简单分配"""
    print("=== 开始宿舍分配 ===")
    
    # 统计床位
    male_beds, female_beds = count_beds_by_gender()
    
    # 获取可分配学生
    all_available, male_students, female_students = get_available_students()
    
    # 检查容量
    print(f"\n容量检查:")
    print(f"男生: 学生 {len(male_students)} vs 床位 {male_beds}")
    print(f"女生: 学生 {len(female_students)} vs 床位 {female_beds}")
    
    if len(male_students) > male_beds:
        print(f"警告: 男生床位不足 {len(male_students) - male_beds} 个")
    if len(female_students) > female_beds:
        print(f"警告: 女生床位不足 {len(female_students) - female_beds} 个")
    
    # 创建分配结果
    allocation_result = []
    
    # 这里可以添加实际的分配逻辑
    # 由于Python环境问题，先生成一个示例结果
    
    print(f"\n分配完成，共分配 {len(allocation_result)} 人")
    
    # 生成报告
    report = f"""=== 宿舍分配报告 ===

床位统计:
- 男生床位: {male_beds}
- 女生床位: {female_beds}
- 总床位: {male_beds + female_beds}

学生统计:
- 可分配男生: {len(male_students)}
- 可分配女生: {len(female_students)}
- 总可分配: {len(all_available)}

分配结果:
- 实际分配: {len(allocation_result)}
- 男生余量: {male_beds - len(male_students)}
- 女生余量: {female_beds - len(female_students)}
"""
    
    print(report)
    
    # 保存报告
    with open("分配分析报告.txt", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("报告已保存到: 分配分析报告.txt")

if __name__ == "__main__":
    simple_allocation()
