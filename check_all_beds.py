#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新检查所有床位数据
"""

import csv
import os

def check_all_bed_files():
    """检查所有可能的床位文件"""
    print("=== 检查所有床位相关文件 ===")
    
    # 可能的文件名
    possible_files = [
        "宿舍情况.csv",
        "宿舍情况_utf8.csv", 
        "宿舍安排.csv",
        "宿舍分配.csv",
        "床位信息.csv",
        "dormitory.csv"
    ]
    
    for filename in possible_files:
        if os.path.exists(filename):
            print(f"\n找到文件: {filename}")
            try:
                with open(filename, 'r', encoding='utf-8-sig', newline='') as f:
                    reader = csv.DictReader(f)
                    rows = list(reader)
                    print(f"  总行数: {len(rows)}")
                    print(f"  列名: {list(reader.fieldnames) if hasattr(reader, 'fieldnames') else '无法获取'}")
                    
                    # 重新读取文件获取列名
                    with open(filename, 'r', encoding='utf-8-sig', newline='') as f2:
                        reader2 = csv.DictReader(f2)
                        print(f"  列名: {reader2.fieldnames}")
                        
                        # 统计床位
                        all_beds = 0
                        available_beds = 0
                        male_beds = 0
                        female_beds = 0
                        
                        for row in reader2:
                            # 检查是否有床位号
                            bed_num = row.get('床位号', '')
                            if bed_num and str(bed_num).strip() and str(bed_num).strip() != 'nan':
                                all_beds += 1
                                
                                # 检查使用情况
                                usage = row.get('使用情况', '')
                                if usage == '空置':
                                    available_beds += 1
                                
                                # 检查性别
                                gender = row.get('性别', '')
                                if gender == '男':
                                    male_beds += 1
                                elif gender == '女':
                                    female_beds += 1
                        
                        print(f"  总床位: {all_beds}")
                        print(f"  空置床位: {available_beds}")
                        print(f"  男生床位: {male_beds}")
                        print(f"  女生床位: {female_beds}")
                        
                        # 显示前几行数据
                        print(f"  前3行数据:")
                        with open(filename, 'r', encoding='utf-8-sig', newline='') as f3:
                            reader3 = csv.DictReader(f3)
                            for i, row in enumerate(reader3):
                                if i < 3:
                                    print(f"    行{i+1}: {dict(row)}")
                                else:
                                    break
                        
            except Exception as e:
                print(f"  读取错误: {e}")
        else:
            print(f"文件不存在: {filename}")

if __name__ == "__main__":
    check_all_bed_files()
