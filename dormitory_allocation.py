#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宿舍分配系统
优先分配已匹配人员，然后随机分配剩余人员（考虑性别和专业）
"""

import pandas as pd
import numpy as np
import random
from collections import defaultdict
import os
import sys

class DormitoryAllocation:
    def __init__(self):
        self.all_students = pd.DataFrame()
        self.exclude_students = pd.DataFrame()
        self.matched_students = pd.DataFrame()
        self.dormitory_info = pd.DataFrame()
        self.allocation_result = []
        
    def load_data(self):
        """加载所有数据文件"""
        try:
            # 合并所有学生数据
            student_files = [
                "法学院人员名单_法学女生.csv",
                "法学院人员名单_法学男生.csv", 
                "法学院人员名单_法外女生.csv",
                "法学院人员名单_法外男生.csv"
            ]
            
            all_students_list = []
            for file in student_files:
                if os.path.exists(file):
                    df = pd.read_csv(file, encoding='utf-8-sig')
                    # 添加专业信息
                    if "法学" in file:
                        df['专业'] = '法学'
                    else:
                        df['专业'] = '法外'
                    all_students_list.append(df)
            
            if all_students_list:
                self.all_students = pd.concat(all_students_list, ignore_index=True)
                # 清理数据
                self.all_students = self.all_students.dropna(subset=['姓名'])
                print(f"加载学生总数: {len(self.all_students)}")
            
            # 加载需排除人员
            if os.path.exists("需排除人员名单_utf8.csv"):
                self.exclude_students = pd.read_csv("需排除人员名单_utf8.csv", encoding='utf-8-sig')
                print(f"需排除人员数: {len(self.exclude_students)}")
            
            # 加载已匹配宿舍人员
            if os.path.exists("已匹配的宿舍_utf8.csv"):
                self.matched_students = pd.read_csv("已匹配的宿舍_utf8.csv", encoding='utf-8-sig')
                print(f"已匹配人员数: {len(self.matched_students)}")
            
            # 加载宿舍信息
            if os.path.exists("宿舍情况_utf8.csv"):
                self.dormitory_info = pd.read_csv("宿舍情况_utf8.csv", encoding='utf-8-sig')
                print(f"可用床位数: {len(self.dormitory_info)}")
                
            return True
            
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return False
    
    def validate_data(self):
        """验证数据完整性和唯一性"""
        errors = []
        
        # 检查需排除人员的唯一性
        if not self.exclude_students.empty:
            duplicate_names = self.exclude_students[self.exclude_students.duplicated(subset=['姓名'], keep=False)]
            if not duplicate_names.empty:
                errors.append(f"需排除人员名单中有重复姓名: {duplicate_names['姓名'].tolist()}")
        
        # 检查已匹配人员是否在学生名单中
        if not self.matched_students.empty and not self.all_students.empty:
            matched_names = set(self.matched_students['姓名'].tolist())
            all_names = set(self.all_students['姓名'].tolist())
            not_found = matched_names - all_names
            if not_found:
                errors.append(f"已匹配人员中有{len(not_found)}人不在学生名单中: {list(not_found)[:10]}...")
        
        # 检查宿舍容量
        if not self.dormitory_info.empty:
            male_beds = len(self.dormitory_info[self.dormitory_info['性别'] == '男'])
            female_beds = len(self.dormitory_info[self.dormitory_info['性别'] == '女'])
            print(f"男生床位: {male_beds}, 女生床位: {female_beds}")
        
        if errors:
            for error in errors:
                print(f"验证错误: {error}")
            return False
        
        print("数据验证通过")
        return True
    
    def get_available_students(self):
        """获取可分配的学生（排除已排除和已匹配的学生）"""
        if self.all_students.empty:
            return pd.DataFrame()
        
        available = self.all_students.copy()
        
        # 排除需排除的学生
        if not self.exclude_students.empty:
            exclude_names = self.exclude_students['姓名'].tolist()
            available = available[~available['姓名'].isin(exclude_names)]
            print(f"排除{len(exclude_names)}名学生后剩余: {len(available)}")
        
        # 排除已匹配的学生
        if not self.matched_students.empty:
            matched_names = self.matched_students['姓名'].tolist()
            available = available[~available['姓名'].isin(matched_names)]
            print(f"排除已匹配学生后剩余: {len(available)}")
        
        return available
    
    def allocate_matched_students(self):
        """分配已匹配的学生到宿舍"""
        if self.matched_students.empty or self.dormitory_info.empty:
            return
        
        print("开始分配已匹配学生...")
        
        # 按宿舍类型和序号分组
        matched_groups = self.matched_students.groupby(['类型', '宿舍序号'])
        
        for (dorm_type, dorm_num), group in matched_groups:
            if pd.isna(dorm_num):
                continue
                
            print(f"分配 {dorm_type} 宿舍 {int(dorm_num)}: {len(group)} 人")
            
            # 获取该组学生的性别
            gender = group['性别'].iloc[0]
            
            # 找到可用的宿舍床位
            available_beds = self.dormitory_info[
                (self.dormitory_info['性别'] == gender) & 
                (self.dormitory_info['使用情况'] == '空置')
            ].head(len(group))
            
            if len(available_beds) >= len(group):
                for i, (_, student) in enumerate(group.iterrows()):
                    bed = available_beds.iloc[i]
                    self.allocation_result.append({
                        '姓名': student['姓名'],
                        '性别': student['性别'],
                        '专业': dorm_type.replace('女生', '').replace('男生', ''),
                        '宿舍楼': bed['宿舍楼'],
                        '房间号': bed['房间号'],
                        '床位号': bed['床位号'],
                        '分配类型': '已匹配'
                    })
                    
                    # 标记床位为已使用
                    self.dormitory_info.loc[
                        (self.dormitory_info['宿舍楼'] == bed['宿舍楼']) &
                        (self.dormitory_info['房间号'] == bed['房间号']) &
                        (self.dormitory_info['床位号'] == bed['床位号']),
                        '使用情况'
                    ] = '已分配'
            else:
                print(f"警告: {dorm_type} 宿舍 {int(dorm_num)} 床位不足")
    
    def allocate_remaining_students(self):
        """随机分配剩余学生"""
        available_students = self.get_available_students()
        
        if available_students.empty:
            print("没有剩余学生需要分配")
            return
        
        print(f"开始随机分配剩余 {len(available_students)} 名学生...")
        
        # 按性别分组
        for gender in ['男', '女']:
            gender_students = available_students[available_students['性别'] == gender]
            if gender_students.empty:
                continue
                
            # 获取该性别的可用床位
            available_beds = self.dormitory_info[
                (self.dormitory_info['性别'] == gender) & 
                (self.dormitory_info['使用情况'] == '空置')
            ]
            
            print(f"{gender}生: {len(gender_students)} 人, 可用床位: {len(available_beds)}")
            
            if len(available_beds) < len(gender_students):
                print(f"警告: {gender}生床位不足，只能分配 {len(available_beds)} 人")
                gender_students = gender_students.head(len(available_beds))
            
            # 随机分配
            students_list = gender_students.to_dict('records')
            beds_list = available_beds.to_dict('records')
            
            random.shuffle(students_list)
            random.shuffle(beds_list)
            
            for i, student in enumerate(students_list):
                if i < len(beds_list):
                    bed = beds_list[i]
                    self.allocation_result.append({
                        '姓名': student['姓名'],
                        '性别': student['性别'],
                        '专业': student.get('专业', '未知'),
                        '宿舍楼': bed['宿舍楼'],
                        '房间号': bed['房间号'],
                        '床位号': bed['床位号'],
                        '分配类型': '随机分配'
                    })
                    
                    # 标记床位为已使用
                    self.dormitory_info.loc[
                        (self.dormitory_info['宿舍楼'] == bed['宿舍楼']) &
                        (self.dormitory_info['房间号'] == bed['房间号']) &
                        (self.dormitory_info['床位号'] == bed['床位号']),
                        '使用情况'
                    ] = '已分配'
    
    def generate_report(self):
        """生成分配报告"""
        if not self.allocation_result:
            print("没有分配结果")
            return
        
        # 保存分配结果
        result_df = pd.DataFrame(self.allocation_result)
        result_df.to_csv("宿舍分配结果.csv", index=False, encoding='utf-8-sig')
        print(f"分配结果已保存到: 宿舍分配结果.csv")
        
        # 生成统计报告
        report = []
        report.append("=== 宿舍分配统计报告 ===\n")
        
        # 总体统计
        total_allocated = len(result_df)
        report.append(f"总分配人数: {total_allocated}")
        
        # 按性别统计
        gender_stats = result_df['性别'].value_counts()
        report.append(f"男生: {gender_stats.get('男', 0)} 人")
        report.append(f"女生: {gender_stats.get('女', 0)} 人")
        
        # 按专业统计
        major_stats = result_df['专业'].value_counts()
        for major, count in major_stats.items():
            report.append(f"{major}: {count} 人")
        
        # 按分配类型统计
        type_stats = result_df['分配类型'].value_counts()
        for alloc_type, count in type_stats.items():
            report.append(f"{alloc_type}: {count} 人")
        
        # 剩余床位统计
        remaining_beds = self.dormitory_info[self.dormitory_info['使用情况'] == '空置']
        remaining_male = len(remaining_beds[remaining_beds['性别'] == '男'])
        remaining_female = len(remaining_beds[remaining_beds['性别'] == '女'])
        report.append(f"\n剩余床位:")
        report.append(f"男生床位: {remaining_male}")
        report.append(f"女生床位: {remaining_female}")
        
        report_text = "\n".join(report)
        print(report_text)
        
        # 保存报告
        with open("分配报告.txt", "w", encoding='utf-8') as f:
            f.write(report_text)
        print(f"统计报告已保存到: 分配报告.txt")
    
    def run_allocation(self):
        """运行完整的分配流程"""
        print("开始宿舍分配...")
        
        # 加载数据
        if not self.load_data():
            return False
        
        # 验证数据
        if not self.validate_data():
            return False
        
        # 分配已匹配学生
        self.allocate_matched_students()
        
        # 分配剩余学生
        self.allocate_remaining_students()
        
        # 生成报告
        self.generate_report()
        
        print("宿舍分配完成！")
        return True

if __name__ == "__main__":
    # 设置随机种子以便结果可重现
    random.seed(42)
    np.random.seed(42)
    
    allocator = DormitoryAllocation()
    allocator.run_allocation()
