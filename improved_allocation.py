#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的宿舍分配系统
解决床位不足和楼层性别混合问题
"""

import csv
import random
import re
from collections import defaultdict

class ImprovedDormitoryAllocator:
    def __init__(self):
        self.students = []
        self.exclude_names = set()
        self.matched_students = []
        self.dormitory_beds = []
        self.allocation_result = []
        self.floor_analysis = {}
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        
        # 加载学生数据
        with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                gender = row.get('性别', '').strip()
                if name and gender:
                    self.students.append(row)
        
        # 加载排除名单
        exclude_file = "需排除人员名单_utf8.csv" if os.path.exists("需排除人员名单_utf8.csv") else "需排除人员名单.csv"
        with open(exclude_file, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                name = row.get('姓名', '').strip()
                if name:
                    self.exclude_names.add(name)
        
        # 加载已匹配学生
        matched_file = "已匹配的宿舍_utf8.csv" if os.path.exists("已匹配的宿舍_utf8.csv") else "已匹配的宿舍.csv"
        with open(matched_file, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                self.matched_students.append(row)
        
        # 加载宿舍信息
        dorm_file = "宿舍情况_utf8.csv" if os.path.exists("宿舍情况_utf8.csv") else "宿舍情况.csv"
        with open(dorm_file, 'r', encoding='utf-8-sig', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 清理数据
                if row.get('床位号') and str(row.get('床位号')).strip():
                    self.dormitory_beds.append(row)
        
        print(f"✓ 学生: {len(self.students)}")
        print(f"✓ 排除: {len(self.exclude_names)}")
        print(f"✓ 已匹配: {len(self.matched_students)}")
        print(f"✓ 床位: {len(self.dormitory_beds)}")
    
    def analyze_floor_distribution(self):
        """分析楼层分布"""
        print("\n=== 分析楼层分布 ===")
        
        # 按宿舍楼和楼层分组
        building_floors = defaultdict(lambda: defaultdict(list))
        
        for bed in self.dormitory_beds:
            building = bed['宿舍楼']
            room_num = bed['房间号']
            
            # 提取楼层号
            floor_match = re.match(r'(\d{1,2})', room_num)
            if floor_match:
                floor = floor_match.group(1)
                building_floors[building][floor].append(bed)
        
        # 分析每个楼层的性别分布
        mixed_floors = []
        gender_conflicts = []
        
        for building, floors in building_floors.items():
            print(f"\n{building}:")
            for floor, beds in sorted(floors.items()):
                male_beds = len([b for b in beds if b['性别'] == '男'])
                female_beds = len([b for b in beds if b['性别'] == '女'])
                
                print(f"  {floor}楼: {len(beds)}床位 (男:{male_beds}, 女:{female_beds})")
                
                if male_beds > 0 and female_beds > 0:
                    mixed_floors.append(f"{building}-{floor}楼")
                    gender_conflicts.append({
                        'building': building,
                        'floor': floor,
                        'male_beds': male_beds,
                        'female_beds': female_beds,
                        'beds': beds
                    })
                    print(f"    ⚠️  性别混合楼层！")
        
        self.floor_analysis = {
            'building_floors': building_floors,
            'mixed_floors': mixed_floors,
            'gender_conflicts': gender_conflicts
        }
        
        if mixed_floors:
            print(f"\n❌ 发现 {len(mixed_floors)} 个性别混合楼层:")
            for floor in mixed_floors:
                print(f"  - {floor}")
        else:
            print(f"\n✓ 所有楼层性别统一")
    
    def resolve_gender_conflicts(self):
        """解决性别冲突"""
        print("\n=== 解决性别冲突 ===")
        
        if not self.floor_analysis['gender_conflicts']:
            print("✓ 无性别冲突需要解决")
            return
        
        # 计算学生需求
        male_need, female_need = self.calculate_student_demand()
        current_male_beds = len([b for b in self.dormitory_beds if b['性别'] == '男'])
        current_female_beds = len([b for b in self.dormitory_beds if b['性别'] == '女'])
        
        print(f"当前床位: 男{current_male_beds}, 女{current_female_beds}")
        print(f"学生需求: 男{male_need}, 女{female_need}")
        
        # 如果女生床位不足，尝试转换
        if female_need > current_female_beds:
            shortage = female_need - current_female_beds
            print(f"女生床位不足 {shortage} 个，尝试转换...")
            
            converted = 0
            for conflict in self.floor_analysis['gender_conflicts']:
                if converted >= shortage:
                    break
                
                # 优先转换床位较少的性别
                if conflict['male_beds'] <= conflict['female_beds']:
                    # 将男生床位转换为女生床位
                    male_beds_in_floor = [b for b in conflict['beds'] if b['性别'] == '男']
                    for bed in male_beds_in_floor:
                        if converted >= shortage:
                            break
                        bed['性别'] = '女'
                        converted += 1
                        print(f"  转换: {conflict['building']}-{conflict['floor']}楼 {bed['房间号']}室{bed['床位号']}号 男→女")
                else:
                    # 将女生床位转换为男生床位（如果男生不足）
                    if male_need > current_male_beds:
                        female_beds_in_floor = [b for b in conflict['beds'] if b['性别'] == '女']
                        convert_count = min(len(female_beds_in_floor), conflict['female_beds'])
                        for i, bed in enumerate(female_beds_in_floor):
                            if i >= convert_count:
                                break
                            bed['性别'] = '男'
                            print(f"  转换: {conflict['building']}-{conflict['floor']}楼 {bed['房间号']}室{bed['床位号']}号 女→男")
            
            print(f"✓ 已转换 {converted} 个床位")
        
        # 重新分析楼层分布
        self.analyze_floor_distribution()
    
    def calculate_student_demand(self):
        """计算学生需求"""
        # 计算实际需要宿舍的学生数
        matched_names = set(s.get('姓名', '') for s in self.matched_students)
        
        male_need = 0
        female_need = 0
        
        for student in self.students:
            name = student['姓名']
            if name not in self.exclude_names:  # 包括已匹配和未匹配的学生
                if student['性别'] == '男':
                    male_need += 1
                else:
                    female_need += 1
        
        return male_need, female_need
    
    def optimize_allocation_by_floor(self):
        """按楼层优化分配"""
        print("\n=== 按楼层优化分配 ===")
        
        # 清空现有分配
        self.allocation_result.clear()
        for bed in self.dormitory_beds:
            if bed.get('使用情况') == '已分配':
                bed['使用情况'] = '空置'
        
        # 按楼层组织床位
        floor_beds = defaultdict(lambda: {'male': [], 'female': []})
        
        for bed in self.dormitory_beds:
            if bed.get('使用情况') == '空置':
                building = bed['宿舍楼']
                room_num = bed['房间号']
                floor_match = re.match(r'(\d{1,2})', room_num)
                if floor_match:
                    floor = floor_match.group(1)
                    floor_key = f"{building}-{floor}"
                    gender = bed['性别']
                    if gender in ['男', '女']:
                        if gender not in floor_beds[floor_key]:
                            floor_beds[floor_key][gender] = []
                        floor_beds[floor_key][gender].append(bed)
        
        # 优先分配已匹配学生
        self.allocate_matched_students_by_floor(floor_beds)
        
        # 分配剩余学生
        self.allocate_remaining_students_by_floor(floor_beds)
    
    def allocate_matched_students_by_floor(self, floor_beds):
        """按楼层分配已匹配学生"""
        print("分配已匹配学生...")
        
        # 按宿舍组分组
        matched_groups = defaultdict(list)
        for student in self.matched_students:
            dorm_type = student.get('类型', '')
            dorm_num = student.get('宿舍序号', '')
            if dorm_num and str(dorm_num).strip() and str(dorm_num).strip() != 'nan':
                key = f"{dorm_type}_{dorm_num}"
                matched_groups[key].append(student)
        
        for group_key, group_students in matched_groups.items():
            if not group_students:
                continue
            
            gender = group_students[0].get('性别')
            gender_key = '男' if gender == '男' else '女'
            
            # 寻找有足够床位的楼层
            allocated = 0
            for floor_key, beds_by_gender in floor_beds.items():
                available_beds = beds_by_gender.get(gender_key, [])
                if len(available_beds) >= len(group_students) - allocated:
                    # 在这个楼层分配剩余学生
                    for i in range(allocated, len(group_students)):
                        if i - allocated < len(available_beds):
                            student = group_students[i]
                            bed = available_beds[i - allocated]
                            
                            self.allocation_result.append({
                                '学号': self._get_student_id(student.get('姓名', '')),
                                '姓名': student.get('姓名', ''),
                                '性别': student.get('性别', ''),
                                '专业': self._get_student_major(student.get('姓名', '')),
                                '宿舍楼': bed.get('宿舍楼', ''),
                                '房间号': bed.get('房间号', ''),
                                '床位号': bed.get('床位号', ''),
                                '分配类型': '已匹配'
                            })
                            bed['使用情况'] = '已分配'
                            allocated += 1
                    
                    # 从可用床位中移除已分配的
                    beds_by_gender[gender_key] = available_beds[allocated:]
                    break
        
        matched_count = len([r for r in self.allocation_result if r['分配类型'] == '已匹配'])
        print(f"✓ 已匹配学生分配完成: {matched_count} 人")
    
    def allocate_remaining_students_by_floor(self, floor_beds):
        """按楼层分配剩余学生"""
        print("分配剩余学生...")
        
        # 获取需要分配的学生
        matched_names = set(s.get('姓名', '') for s in self.matched_students)
        
        remaining_students = []
        for student in self.students:
            name = student['姓名']
            if name not in self.exclude_names and name not in matched_names:
                remaining_students.append(student)
        
        male_remaining = [s for s in remaining_students if s['性别'] == '男']
        female_remaining = [s for s in remaining_students if s['性别'] == '女']
        
        # 随机打乱
        random.shuffle(male_remaining)
        random.shuffle(female_remaining)
        
        # 分配男生
        self._allocate_gender_by_floor(male_remaining, floor_beds, '男')
        
        # 分配女生
        self._allocate_gender_by_floor(female_remaining, floor_beds, '女')
        
        random_count = len([r for r in self.allocation_result if r['分配类型'] == '随机分配'])
        print(f"✓ 随机分配完成: {random_count} 人")
    
    def _allocate_gender_by_floor(self, students, floor_beds, gender):
        """按楼层分配特定性别学生"""
        allocated = 0
        
        # 按楼层分配，优先使用床位多的楼层
        floor_list = [(floor, beds.get(gender, [])) for floor, beds in floor_beds.items() if beds.get(gender, [])]
        floor_list.sort(key=lambda x: len(x[1]), reverse=True)
        
        for floor_key, available_beds in floor_list:
            for bed in available_beds:
                if allocated >= len(students):
                    break
                
                student = students[allocated]
                self.allocation_result.append({
                    '学号': student['学号'],
                    '姓名': student['姓名'],
                    '性别': student['性别'],
                    '专业': student['专业'],
                    '宿舍楼': bed.get('宿舍楼', ''),
                    '房间号': bed.get('房间号', ''),
                    '床位号': bed.get('床位号', ''),
                    '分配类型': '随机分配'
                })
                bed['使用情况'] = '已分配'
                allocated += 1
            
            if allocated >= len(students):
                break
        
        if allocated < len(students):
            unallocated = len(students) - allocated
            print(f"⚠️  {gender}生有 {unallocated} 人无法分配床位")
    
    def _get_student_id(self, name):
        """根据姓名获取学号"""
        for student in self.students:
            if student['姓名'] == name:
                return student.get('学号', '')
        return ''
    
    def _get_student_major(self, name):
        """根据姓名获取专业"""
        for student in self.students:
            if student['姓名'] == name:
                return student.get('专业', '')
        return ''
    
    def generate_report(self):
        """生成报告"""
        print("\n=== 生成分配报告 ===")
        
        # 保存分配结果
        with open("改进分配结果.csv", 'w', encoding='utf-8-sig', newline='') as f:
            fieldnames = ['学号', '姓名', '性别', '专业', '宿舍楼', '房间号', '床位号', '分配类型']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.allocation_result)
        
        # 统计信息
        total = len(self.allocation_result)
        male_count = len([r for r in self.allocation_result if r['性别'] == '男'])
        female_count = len([r for r in self.allocation_result if r['性别'] == '女'])
        matched_count = len([r for r in self.allocation_result if r['分配类型'] == '已匹配'])
        random_count = len([r for r in self.allocation_result if r['分配类型'] == '随机分配'])
        
        remaining_beds = len([b for b in self.dormitory_beds if b.get('使用情况') == '空置'])
        remaining_male_beds = len([b for b in self.dormitory_beds 
                                  if b.get('使用情况') == '空置' and b.get('性别') == '男'])
        remaining_female_beds = len([b for b in self.dormitory_beds 
                                    if b.get('使用情况') == '空置' and b.get('性别') == '女'])
        
        report = f"""=== 改进版宿舍分配报告 ===

分配总结:
- 总分配人数: {total}
- 男生: {male_count} 人
- 女生: {female_count} 人

按分配方式:
- 已匹配分配: {matched_count} 人
- 随机分配: {random_count} 人

剩余床位:
- 总剩余: {remaining_beds} 个
- 男生床位: {remaining_male_beds} 个
- 女生床位: {remaining_female_beds} 个

楼层性别统一性: {"✓ 已优化" if not self.floor_analysis['mixed_floors'] else "⚠️ 仍有混合楼层"}

生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        print(report)
        
        with open("改进分配报告.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✓ 报告已保存")
    
    def run(self):
        """运行改进的分配流程"""
        print("=== 改进版宿舍分配系统 ===\n")
        
        random.seed(42)
        
        self.load_data()
        self.analyze_floor_distribution()
        self.resolve_gender_conflicts()
        self.optimize_allocation_by_floor()
        self.generate_report()
        
        print("\n=== 分配完成 ===")

if __name__ == "__main__":
    import os
    allocator = ImprovedDormitoryAllocator()
    allocator.run()
