#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据问题：床位重复分配和学生同名问题
"""

import csv
from collections import defaultdict, Counter

def check_duplicate_bed_assignments():
    """检查床位重复分配问题"""
    print("=== 检查床位重复分配问题 ===")
    
    bed_assignments = defaultdict(list)
    
    # 读取分配结果
    with open("改进分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            bed_key = f"{row['宿舍楼']}_{row['房间号']}_{row['床位号']}"
            bed_assignments[bed_key].append(row['姓名'])
    
    # 找出重复分配的床位
    duplicate_beds = []
    for bed_key, students in bed_assignments.items():
        if len(students) > 1:
            duplicate_beds.append((bed_key, students))
    
    if duplicate_beds:
        print(f"❌ 发现 {len(duplicate_beds)} 个床位被重复分配:")
        for bed_key, students in duplicate_beds[:10]:  # 只显示前10个
            building, room, bed_num = bed_key.split('_')
            print(f"  {building} {room}室 {bed_num}号床: {len(students)}人 - {students}")
        
        if len(duplicate_beds) > 10:
            print(f"  ... 还有 {len(duplicate_beds) - 10} 个重复床位")
    else:
        print("✅ 没有发现床位重复分配")
    
    return duplicate_beds

def check_duplicate_student_names():
    """检查学生姓名重复问题"""
    print("\n=== 检查学生姓名重复问题 ===")
    
    # 读取学生名单
    students = []
    with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            students.append(row)
    
    # 统计姓名出现次数
    name_counts = Counter(student['姓名'] for student in students)
    duplicate_names = [(name, count) for name, count in name_counts.items() if count > 1]
    
    if duplicate_names:
        print(f"❌ 发现 {len(duplicate_names)} 个重复姓名:")
        for name, count in duplicate_names:
            print(f"  {name}: {count}人")
            
            # 显示重复姓名学生的详细信息
            same_name_students = [s for s in students if s['姓名'] == name]
            for student in same_name_students:
                print(f"    学号: {student['学号']}, 性别: {student['性别']}, 专业: {student['专业']}")
    else:
        print("✅ 没有发现重复姓名")
    
    return duplicate_names

def check_allocation_result_integrity():
    """检查分配结果完整性"""
    print("\n=== 检查分配结果完整性 ===")
    
    # 读取分配结果
    allocation_results = []
    with open("改进分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            allocation_results.append(row)
    
    print(f"分配结果总数: {len(allocation_results)}")
    
    # 检查学号唯一性
    student_ids = [result['学号'] for result in allocation_results]
    id_counts = Counter(student_ids)
    duplicate_ids = [(sid, count) for sid, count in id_counts.items() if count > 1]
    
    if duplicate_ids:
        print(f"❌ 发现 {len(duplicate_ids)} 个重复学号:")
        for sid, count in duplicate_ids[:5]:
            print(f"  学号 {sid}: 分配了 {count} 次")
    else:
        print("✅ 学号唯一性正常")
    
    # 检查姓名唯一性
    student_names = [result['姓名'] for result in allocation_results]
    name_counts = Counter(student_names)
    duplicate_names_in_result = [(name, count) for name, count in name_counts.items() if count > 1]
    
    if duplicate_names_in_result:
        print(f"❌ 分配结果中发现 {len(duplicate_names_in_result)} 个重复姓名:")
        for name, count in duplicate_names_in_result[:5]:
            print(f"  {name}: 分配了 {count} 次")
    else:
        print("✅ 分配结果中姓名唯一性正常")

def check_bed_data_integrity():
    """检查床位数据完整性"""
    print("\n=== 检查床位数据完整性 ===")
    
    # 读取宿舍数据
    beds = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            beds.append(row)
    
    print(f"床位数据总数: {len(beds)}")
    
    # 检查床位唯一性
    bed_keys = []
    for bed in beds:
        if bed.get('床位号') and str(bed.get('床位号')).strip():
            bed_key = f"{bed['宿舍楼']}_{bed['房间号']}_{bed['床位号']}"
            bed_keys.append(bed_key)
    
    bed_counts = Counter(bed_keys)
    duplicate_bed_keys = [(key, count) for key, count in bed_counts.items() if count > 1]
    
    if duplicate_bed_keys:
        print(f"❌ 发现 {len(duplicate_bed_keys)} 个重复床位:")
        for key, count in duplicate_bed_keys[:5]:
            building, room, bed_num = key.split('_')
            print(f"  {building} {room}室 {bed_num}号床: 出现 {count} 次")
    else:
        print("✅ 床位数据唯一性正常")
    
    # 检查床位号格式
    invalid_bed_nums = []
    for bed in beds:
        bed_num = bed.get('床位号', '')
        if not bed_num or str(bed_num).strip() == '' or str(bed_num).strip() == 'nan':
            invalid_bed_nums.append(bed)
    
    if invalid_bed_nums:
        print(f"❌ 发现 {len(invalid_bed_nums)} 个无效床位号")
    else:
        print("✅ 床位号格式正常")

def analyze_allocation_algorithm_issues():
    """分析分配算法问题"""
    print("\n=== 分析分配算法问题 ===")
    
    print("可能的问题原因:")
    print("1. 床位分配时没有正确标记为'已分配'")
    print("2. 同一个床位对象被多次使用")
    print("3. 床位列表没有正确更新")
    print("4. 学生姓名重复导致查找错误")
    
    print("\n建议解决方案:")
    print("1. 使用学号而不是姓名作为唯一标识")
    print("2. 分配床位后立即从可用列表中移除")
    print("3. 添加床位唯一性检查")
    print("4. 处理学生姓名重复问题")

def main():
    print("=== 数据问题检查报告 ===\n")
    
    # 检查床位重复分配
    duplicate_beds = check_duplicate_bed_assignments()
    
    # 检查学生姓名重复
    duplicate_names = check_duplicate_student_names()
    
    # 检查分配结果完整性
    check_allocation_result_integrity()
    
    # 检查床位数据完整性
    check_bed_data_integrity()
    
    # 分析算法问题
    analyze_allocation_algorithm_issues()
    
    print(f"\n=== 总结 ===")
    if duplicate_beds:
        print(f"❌ 严重问题: {len(duplicate_beds)} 个床位被重复分配")
    if duplicate_names:
        print(f"⚠️  数据问题: {len(duplicate_names)} 个重复姓名")
    
    if not duplicate_beds and not duplicate_names:
        print("✅ 数据检查通过")
    else:
        print("❌ 需要修复分配算法")

if __name__ == "__main__":
    main()
