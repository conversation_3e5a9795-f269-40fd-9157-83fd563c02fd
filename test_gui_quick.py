#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试GUI程序是否能正常导入和初始化
"""

def test_gui_import():
    """测试GUI程序导入"""
    try:
        print("正在测试GUI程序导入...")
        
        # 测试导入
        import dormitory_gui
        print("✅ GUI程序导入成功")
        
        # 测试fixed_allocation导入
        from fixed_allocation import FixedDormitoryAllocator
        print("✅ 修复版分配算法导入成功")
        
        # 测试创建分配器实例
        allocator = FixedDormitoryAllocator()
        print("✅ 分配器实例创建成功")
        
        print("\n🎉 所有测试通过！GUI程序已正确集成最新的修复版脚本")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    test_gui_import()
