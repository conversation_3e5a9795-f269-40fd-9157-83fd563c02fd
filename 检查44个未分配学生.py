#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查44个未分配学生的具体情况
"""

import csv
from collections import defaultdict

def check_unallocated_students():
    """检查未分配的学生"""
    print("=== 检查44个未分配学生 ===")
    
    # 1. 加载法学院人员名单（328人）
    all_law_students = []
    with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_law_students.append(row)
    
    print(f"法学院人员名单总数: {len(all_law_students)}")
    
    # 2. 加载已匹配学生（248人）
    matched_students = []
    with open("已匹配的宿舍_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            matched_students.append(row)
    
    print(f"已匹配学生数: {len(matched_students)}")
    
    # 3. 加载完美分配结果（284人）
    allocated_students = []
    with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            allocated_students.append(row)
    
    print(f"完美分配结果: {len(allocated_students)}")
    
    # 4. 创建学生集合（使用学号作为唯一标识）
    all_law_ids = set(s['学号'] for s in all_law_students)
    matched_ids = set(s.get('学号', '') for s in matched_students if s.get('学号'))
    allocated_ids = set(s['学号'] for s in allocated_students)
    
    print(f"\n=== 学号统计 ===")
    print(f"法学院学号数: {len(all_law_ids)}")
    print(f"已匹配学号数: {len(matched_ids)}")
    print(f"分配结果学号数: {len(allocated_ids)}")
    
    # 5. 检查已匹配学生是否都在法学院名单中
    matched_in_law = matched_ids & all_law_ids
    matched_not_in_law = matched_ids - all_law_ids
    
    print(f"\n=== 已匹配学生分析 ===")
    print(f"已匹配学生在法学院名单中: {len(matched_in_law)}")
    print(f"已匹配学生不在法学院名单中: {len(matched_not_in_law)}")
    
    if matched_not_in_law:
        print(f"不在法学院名单的已匹配学生学号: {list(matched_not_in_law)[:5]}...")
    
    # 6. 找出未分配的学生
    unallocated_law_students = all_law_ids - allocated_ids
    
    print(f"\n=== 未分配学生分析 ===")
    print(f"法学院学生中未分配的: {len(unallocated_law_students)} 人")
    
    if unallocated_law_students:
        print(f"\n未分配学生详情:")
        unallocated_details = []
        for student in all_law_students:
            if student['学号'] in unallocated_law_students:
                unallocated_details.append(student)
        
        # 按性别分类
        male_unallocated = [s for s in unallocated_details if s['性别'] == '男']
        female_unallocated = [s for s in unallocated_details if s['性别'] == '女']
        
        print(f"未分配男生: {len(male_unallocated)} 人")
        print(f"未分配女生: {len(female_unallocated)} 人")
        
        print(f"\n前10个未分配学生:")
        for i, student in enumerate(unallocated_details[:10]):
            print(f"  {i+1}. {student['姓名']} ({student['学号']}) - {student['性别']} - {student['专业']}")
        
        if len(unallocated_details) > 10:
            print(f"  ... 还有 {len(unallocated_details) - 10} 人")
    
    # 7. 检查分配结果中的学生来源
    allocated_from_law = allocated_ids & all_law_ids
    allocated_from_matched = allocated_ids & matched_ids
    allocated_not_from_law = allocated_ids - all_law_ids
    
    print(f"\n=== 分配结果来源分析 ===")
    print(f"分配结果中来自法学院的: {len(allocated_from_law)} 人")
    print(f"分配结果中来自已匹配的: {len(allocated_from_matched)} 人")
    print(f"分配结果中不来自法学院的: {len(allocated_not_from_law)} 人")
    
    if allocated_not_from_law:
        print(f"不来自法学院的分配学生学号: {list(allocated_not_from_law)[:5]}...")
    
    # 8. 检查床位容量是否足够
    print(f"\n=== 床位容量检查 ===")
    
    # 加载床位信息
    available_beds = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if (row.get('床位号') and 
                str(row.get('床位号')).strip() and 
                str(row.get('床位号')).strip() != 'nan' and
                row.get('使用情况') == '空置'):
                available_beds.append(row)
    
    male_beds = len([b for b in available_beds if b['性别'] == '男'])
    female_beds = len([b for b in available_beds if b['性别'] == '女'])
    
    print(f"可用床位总数: {len(available_beds)}")
    print(f"可用男生床位: {male_beds}")
    print(f"可用女生床位: {female_beds}")
    
    # 计算需求
    if unallocated_law_students:
        male_need = len([s for s in unallocated_details if s['性别'] == '男'])
        female_need = len([s for s in unallocated_details if s['性别'] == '女'])
        
        print(f"\n未分配学生需求:")
        print(f"男生需求: {male_need}, 可用床位: {male_beds - len([s for s in allocated_students if s['性别'] == '男'])}")
        print(f"女生需求: {female_need}, 可用床位: {female_beds - len([s for s in allocated_students if s['性别'] == '女'])}")
    
    # 9. 分析为什么这些学生没有被分配
    print(f"\n=== 未分配原因分析 ===")
    
    if len(unallocated_law_students) > 0:
        print(f"可能的原因:")
        print(f"1. 床位容量不足")
        print(f"2. 性别床位分布不匹配")
        print(f"3. 算法筛选条件过严")
        print(f"4. 数据质量问题")
        
        # 检查这些学生是否在排除名单中
        exclude_names = set()
        try:
            with open("需排除人员名单_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    exclude_names.add(row.get('姓名', ''))
        except:
            pass
        
        unallocated_in_exclude = 0
        for student in unallocated_details:
            if student['姓名'] in exclude_names:
                unallocated_in_exclude += 1
        
        if unallocated_in_exclude > 0:
            print(f"5. 其中 {unallocated_in_exclude} 人在排除名单中（算法正确排除）")
    
    return {
        'total_law_students': len(all_law_students),
        'matched_students': len(matched_students),
        'allocated_students': len(allocated_students),
        'unallocated_count': len(unallocated_law_students),
        'unallocated_details': unallocated_details if unallocated_law_students else []
    }

def check_allocation_algorithm():
    """检查分配算法是否有问题"""
    print(f"\n=== 检查分配算法 ===")
    
    # 检查fixed_allocation.py中的逻辑
    print(f"建议检查的算法问题:")
    print(f"1. 学生数据加载是否完整")
    print(f"2. 排除名单处理是否正确")
    print(f"3. 床位分配逻辑是否有遗漏")
    print(f"4. 性别匹配是否严格")
    print(f"5. 已匹配学生处理是否正确")

if __name__ == "__main__":
    result = check_unallocated_students()
    check_allocation_algorithm()
    
    print(f"\n=== 总结 ===")
    print(f"应该分配: {result['total_law_students']} 人")
    print(f"实际分配: {result['allocated_students']} 人")
    print(f"未分配: {result['unallocated_count']} 人")
    
    if result['unallocated_count'] > 0:
        print(f"❌ 需要解决 {result['unallocated_count']} 个学生的分配问题")
    else:
        print(f"✅ 所有学生都已分配")
