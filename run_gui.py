#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动宿舍分配系统GUI
"""

import sys
import os

def check_dependencies():
    """检查依赖"""
    try:
        import tkinter
        import pandas
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install pandas openpyxl")
        return False

def check_data_files():
    """检查数据文件"""
    required_files = [
        "法学院人员名单.csv",
        "需排除人员名单.csv", 
        "已匹配的宿舍.csv",
        "宿舍情况.csv"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"❌ {file} - 缺失")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n警告: 缺失 {len(missing_files)} 个必需文件")
        print("请确保所有数据文件都在当前目录中")
        return False
    
    print("✓ 所有数据文件都存在")
    return True

def main():
    print("=== 宿舍分配系统GUI启动器 ===\n")
    
    # 检查依赖
    print("检查依赖...")
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("\n检查数据文件...")
    if not check_data_files():
        choice = input("\n是否仍要启动GUI? (y/N): ")
        if choice.lower() != 'y':
            return
    
    print("\n启动GUI...")
    try:
        from dormitory_gui import main as gui_main
        gui_main()
    except Exception as e:
        print(f"启动GUI时出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
