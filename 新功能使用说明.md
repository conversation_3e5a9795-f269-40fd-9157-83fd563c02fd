# 宿舍分配系统新功能使用说明

## 🎉 新增功能概览

我已经为您的宿舍分配系统添加了两个重要的新功能：

### 1. 批量宿舍对调功能 🔄
### 2. 学生信息编辑功能 ✏️

---

## 🔄 宿舍对调功能

### 功能描述
可以将两个房间的所有学生进行对调，例如603宿舍的学生和506宿舍的学生完全交换。

### 使用方法

#### 方法一：使用顶部按钮（推荐）
1. 在GUI主界面点击 **"宿舍对调"** 按钮
2. 在弹出的对话框中选择要对调的两个房间
3. 点击 **"预览"** 查看对调效果
4. 确认无误后点击 **"执行对调"**

#### 方法二：使用宿舍管理标签页
1. 切换到 **"宿舍管理"** 标签页
2. 在宿舍对调区域选择两个房间
3. 点击 **"预览对调"** 查看详细信息
4. 点击 **"执行对调"** 完成操作

### 功能特点
- ✅ **智能预览**：显示对调前后的详细学生信息
- ✅ **性别冲突检测**：自动检测对调后是否存在性别冲突
- ✅ **实时更新**：对调完成后立即刷新显示
- ✅ **安全确认**：执行前需要用户确认

### 使用示例
```
对调前：
- 603房间：张三(男)、李四(男)、王五(男)、赵六(男)
- 506房间：小红(女)、小明(女)、小丽(女)、小芳(女)

对调后：
- 603房间：小红(女)、小明(女)、小丽(女)、小芳(女)
- 506房间：张三(男)、李四(男)、王五(男)、赵六(男)
```

---

## ✏️ 学生信息编辑功能

### 功能描述
可以修改学生的基本信息和宿舍分配信息，包括姓名、学号、性别、专业、宿舍楼、房间号、床位号等。

### 使用方法
1. 在GUI主界面点击 **"编辑学生"** 按钮
2. 在学生列表中搜索或选择要编辑的学生
3. 点击 **"编辑选中学生"** 打开编辑表单
4. 修改需要更改的信息
5. 点击 **"保存"** 完成修改

### 可编辑字段

#### 基本信息
- **姓名**：学生姓名
- **学号**：学生学号
- **性别**：男/女（下拉选择）
- **专业**：学生专业
- **年级**：学生年级
- **民族**：学生民族

#### 宿舍信息
- **宿舍楼**：所在宿舍楼
- **房间号**：房间号码
- **床位号**：床位编号
- **分配类型**：已匹配/随机分配/手动调整（下拉选择）

### 功能特点
- ✅ **智能搜索**：支持按姓名或学号搜索学生
- ✅ **表单验证**：确保数据格式正确
- ✅ **实时更新**：修改后立即刷新显示
- ✅ **操作安全**：修改前可以取消操作

---

## 🚀 使用流程建议

### 典型使用场景

#### 场景1：房间对调
```
需求：603房间和506房间的学生需要对调
操作：宿舍对调 → 选择房间 → 预览 → 执行
```

#### 场景2：单个学生调整
```
需求：张三需要从603房间调到506房间
操作：编辑学生 → 搜索张三 → 修改宿舍信息 → 保存
```

#### 场景3：批量信息更正
```
需求：多个学生的专业信息需要更正
操作：编辑学生 → 逐个搜索 → 修改专业 → 保存
```

---

## ⚠️ 注意事项

### 宿舍对调注意事项
1. **性别冲突**：系统会自动检测对调后是否存在性别冲突
2. **床位数量**：确保两个房间的床位数量匹配
3. **数据备份**：重要操作前建议先保存当前结果

### 学生编辑注意事项
1. **学号唯一性**：确保修改后的学号不与其他学生重复
2. **宿舍信息**：修改宿舍信息时确保目标床位可用
3. **数据一致性**：修改后检查相关信息是否一致

---

## 🔧 技术实现

### 新增按钮
- 在主界面顶部添加了 **"宿舍对调"** 和 **"编辑学生"** 按钮
- 集成到现有的GUI框架中

### 新增标签页
- 添加了 **"宿舍管理"** 标签页
- 提供更专业的宿舍管理界面

### 数据处理
- 实时更新分配结果数据
- 自动刷新所有相关显示
- 保持数据一致性

---

## 📞 使用支持

如果在使用过程中遇到问题，请检查：

1. **数据完整性**：确保已经完成宿舍分配
2. **选择正确**：确认选择了正确的房间或学生
3. **权限问题**：确保有文件写入权限
4. **界面刷新**：操作完成后检查界面是否正确刷新

---

## 🎯 功能优势

### 提高效率
- **批量操作**：一次对调整个房间，无需逐个调整
- **智能搜索**：快速定位需要编辑的学生
- **预览功能**：操作前可以预览效果

### 降低错误
- **自动检测**：自动检测性别冲突等问题
- **确认机制**：重要操作需要用户确认
- **数据验证**：确保修改的数据格式正确

### 用户友好
- **直观界面**：清晰的操作界面和提示信息
- **实时反馈**：操作结果立即显示
- **灵活操作**：支持多种操作方式

现在您可以使用这些新功能来更灵活地管理宿舍分配了！🎉
