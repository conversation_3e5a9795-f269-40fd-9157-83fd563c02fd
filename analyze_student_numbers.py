#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析学生总数和分配数差异
"""

import csv

def analyze_student_numbers():
    """分析学生数量差异"""
    print("=== 学生数量分析 ===")
    
    # 1. 加载所有学生
    all_students = []
    with open("法学院人员名单.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_students.append(row)
    
    print(f"学生总数: {len(all_students)}")
    
    # 2. 加载排除名单
    exclude_names = set()
    with open("需排除人员名单_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            name = row.get('姓名', '').strip()
            if name:
                exclude_names.add(name)
    
    print(f"排除学生数: {len(exclude_names)}")
    
    # 3. 计算需要分配的学生
    need_allocation = []
    for student in all_students:
        if student['姓名'] not in exclude_names:
            need_allocation.append(student)
    
    print(f"需要分配学生数: {len(need_allocation)}")
    
    # 4. 按性别统计
    male_need = len([s for s in need_allocation if s['性别'] == '男'])
    female_need = len([s for s in need_allocation if s['性别'] == '女'])
    
    print(f"需要分配男生: {male_need}")
    print(f"需要分配女生: {female_need}")
    
    # 5. 加载完美分配结果
    allocated_students = []
    with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            allocated_students.append(row)
    
    print(f"实际分配学生数: {len(allocated_students)}")
    
    # 6. 找出未分配的学生
    allocated_ids = set(s['学号'] for s in allocated_students)
    need_allocation_ids = set(s['学号'] for s in need_allocation)
    
    not_allocated = need_allocation_ids - allocated_ids
    
    print(f"未分配学生数: {len(not_allocated)}")
    
    if not_allocated:
        print(f"\n未分配学生详情:")
        for i, student_id in enumerate(list(not_allocated)[:10]):
            # 找到学生信息
            student = next((s for s in need_allocation if s['学号'] == student_id), None)
            if student:
                print(f"  {i+1}. {student['姓名']} ({student_id}) - {student['性别']} - {student['专业']}")
        
        if len(not_allocated) > 10:
            print(f"  ... 还有 {len(not_allocated) - 10} 人")
    
    # 7. 检查床位容量
    available_beds = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if (row.get('床位号') and 
                str(row.get('床位号')).strip() and 
                str(row.get('床位号')).strip() != 'nan' and
                row.get('使用情况') == '空置'):
                available_beds.append(row)
    
    male_beds = len([b for b in available_beds if b['性别'] == '男'])
    female_beds = len([b for b in available_beds if b['性别'] == '女'])
    
    print(f"\n床位容量分析:")
    print(f"可用男生床位: {male_beds}")
    print(f"可用女生床位: {female_beds}")
    print(f"总可用床位: {len(available_beds)}")
    
    print(f"\n供需对比:")
    print(f"男生: 需求{male_need} vs 床位{male_beds} = {male_beds - male_need}")
    print(f"女生: 需求{female_need} vs 床位{female_beds} = {female_beds - female_need}")
    
    # 8. 分析床位号为空的记录
    print(f"\n=== 床位号为空记录分析 ===")
    empty_bed_records = []
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for i, row in enumerate(reader, 1):
            bed_num = row.get('床位号', '')
            if not bed_num or str(bed_num).strip() == '' or str(bed_num).strip() == 'nan':
                empty_bed_records.append((i, row))
    
    print(f"床位号为空的记录数: {len(empty_bed_records)}")
    for i, (line_num, record) in enumerate(empty_bed_records):
        print(f"  {i+1}. 第{line_num}行: {record['宿舍楼']} {record['房间号']}室 床位号为空 - {record['使用情况']} - {record['性别']}")

if __name__ == "__main__":
    analyze_student_numbers()
