# 宿舍分配系统GUI使用说明

## 启动方法

### 方法1：双击批处理文件（推荐）
```
双击 run_gui.bat
```

### 方法2：命令行启动
```bash
python run_gui.py
```

### 方法3：直接启动GUI
```bash
python dormitory_gui.py
```

## 界面介绍

### 主界面布局
- **顶部工具栏**：包含主要操作按钮
- **三个标签页**：分配结果、宿舍管理、统计信息
- **底部状态栏**：显示当前操作状态

### 工具栏按钮
- **加载数据**：从CSV文件加载学生和宿舍数据
- **分析问题**：分析宿舍分配中的潜在问题
- **🌟 完美分配**：运行修复版分配算法（强烈推荐）
- **智能分配**：运行改进的智能分配算法
- **基础分配**：运行基础的自动分配算法
- **保存结果**：保存分配结果到CSV文件
- **导出Excel**：导出结果到Excel文件

## 功能详解

### 1. 分配结果标签页

#### 主要功能
- 显示所有学生的宿舍分配结果
- 支持按姓名、学号、宿舍楼等搜索
- 支持手动调整学生宿舍

#### 表格列说明
- **学号**：学生学号
- **姓名**：学生姓名
- **性别**：男/女
- **专业**：法学/法外
- **宿舍楼**：具体宿舍楼名称
- **房间号**：房间号码
- **床位号**：床位编号（1-4）
- **分配类型**：已匹配/随机分配/手动调整

#### 搜索功能
在搜索框中输入关键词，可以搜索：
- 学生姓名
- 学号
- 宿舍楼名称
- 房间号

#### 手动调整功能
1. 选中要调整的学生
2. 点击"手动调整"按钮
3. 在弹出窗口中选择新的床位
4. 点击"确认调整"

### 2. 宿舍管理标签页

#### 主要功能
- 显示所有宿舍床位的使用情况
- 支持按宿舍楼、性别、状态筛选
- 实时显示床位分配状态

#### 筛选选项
- **宿舍楼**：选择特定宿舍楼或查看全部
- **性别**：筛选男生或女生宿舍
- **状态**：筛选空置或已分配床位

#### 表格列说明
- **宿舍楼**：宿舍楼名称
- **房间号**：房间号码
- **床位号**：床位编号
- **使用情况**：空置/已分配
- **性别**：该床位的性别限制
- **学生姓名**：已分配学生的姓名

### 3. 统计信息标签页

#### 显示内容
- **基本统计**：学生总数、排除数、匹配数、分配数
- **床位统计**：总床位、男女床位分布、使用情况
- **分配详情**：按性别和分配方式的统计
- **宿舍楼统计**：各宿舍楼的床位使用情况

## 操作流程

### 首次使用（推荐流程）
1. 确保所有数据文件在程序目录中
2. 启动GUI程序
3. 点击"加载数据"按钮
4. 点击"分析问题"查看潜在问题
5. 点击"智能分配"执行改进的分配算法
6. 查看分配结果，必要时进行手动调整
7. 点击"保存结果"保存分配结果

### 基础分配流程
1. 加载数据
2. 点击"基础分配"执行传统算法
3. 手动处理可能出现的问题
4. 保存结果

### 手动调整流程
1. 在"分配结果"标签页找到要调整的学生
2. 选中该学生记录
3. 点击"手动调整"按钮
4. 在弹出窗口中选择新的床位
5. 确认调整
6. 系统自动更新所有相关显示

### 数据更新流程
1. 更新CSV数据文件
2. 重新启动程序或点击"加载数据"
3. 重新执行分配
4. 保存新的分配结果

## 数据文件要求

### 必需文件
1. **法学院人员名单.csv**
   - 格式：学号,姓名,性别,专业
   - 示例：20250001,张三,男,法学

2. **需排除人员名单.csv**
   - 格式：姓名,性别
   - 示例：李四,女

3. **已匹配的宿舍.csv**
   - 格式：姓名,性别,类型,宿舍序号
   - 示例：王五,男,法学男生,1

4. **宿舍情况.csv**
   - 格式：宿舍楼,房间号,床位号,使用情况,性别
   - 示例：格致书院E座,504,1,空置,女

### 编码要求
- 所有CSV文件必须使用UTF-8编码
- 如有编码问题，可先运行 `extract_data.py` 转换编码

## 输出文件

### 自动生成文件
1. **宿舍分配结果.csv**：详细的分配结果
2. **宿舍分配报告.txt**：统计报告
3. **Excel文件**：通过"导出Excel"功能生成

### Excel导出内容
- **分配结果**工作表：完整的学生分配信息
- **宿舍情况**工作表：所有床位的使用状态

## 注意事项

### 分配规则
- 严格按性别分配，不会出现混合宿舍
- 已匹配学生优先分配
- 剩余学生随机分配到空置床位

### 手动调整限制
- 只能调整到同性别的空置床位
- 调整后原床位自动释放
- 调整记录会标记为"手动调整"

### 数据安全
- 程序不会修改原始数据文件
- 所有更改只影响分配结果
- 建议定期备份分配结果

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python环境是否正确安装
   - 确认pandas库已安装：`pip install pandas openpyxl`

2. **数据加载失败**
   - 检查CSV文件是否存在
   - 确认文件编码为UTF-8
   - 检查文件格式是否正确

3. **分配结果异常**
   - 检查床位数据是否完整
   - 确认性别字段填写正确
   - 验证学生数据的完整性

4. **手动调整失败**
   - 确认选择的是空置床位
   - 检查性别是否匹配
   - 重新加载数据后再试

### 技术支持
如遇到其他问题：
1. 查看程序状态栏的错误信息
2. 检查统计信息标签页的数据
3. 确认所有输入文件格式正确
4. 重启程序重新加载数据

## 系统要求

- **操作系统**：Windows 7/10/11, macOS, Linux
- **Python版本**：3.6或更高
- **必需库**：tkinter（通常内置）, pandas, openpyxl
- **内存**：建议2GB以上
- **存储**：至少50MB可用空间

---

**版本**：v1.0  
**最后更新**：2025年8月14日
