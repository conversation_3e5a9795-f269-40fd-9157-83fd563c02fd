#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析已匹配学生的分配问题
"""

import csv
import os
from collections import defaultdict

def analyze_matching_issues():
    """分析已匹配学生的分配问题"""
    print("=== 分析已匹配学生分配问题 ===\n")
    
    # 1. 加载已匹配学生数据
    print("📋 加载已匹配学生数据")
    print("-" * 50)
    
    matched_students = []
    matched_file = "已匹配的宿舍_utf8.csv" if os.path.exists("已匹配的宿舍_utf8.csv") else "已匹配的宿舍.csv"
    
    with open(matched_file, 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            matched_students.append(row)
    
    print(f"已匹配学生总数: {len(matched_students)}")
    
    # 按组分类
    matched_groups = defaultdict(list)
    for student in matched_students:
        dorm_type = student.get('类型', '')
        dorm_num = student.get('宿舍序号', '')
        if dorm_num and str(dorm_num).strip() and str(dorm_num).strip() != 'nan':
            key = f"{dorm_type}_{dorm_num}"
            matched_groups[key].append(student)
    
    print(f"已匹配组数: {len(matched_groups)}")
    
    # 2. 加载分配结果
    print(f"\n🎯 加载分配结果")
    print("-" * 50)
    
    allocation_result = []
    with open("修复分配结果.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            allocation_result.append(row)
    
    print(f"分配结果总数: {len(allocation_result)}")
    
    # 按分配类型统计
    matched_allocated = [r for r in allocation_result if r.get('分配类型') == '已匹配']
    random_allocated = [r for r in allocation_result if r.get('分配类型') == '随机分配']
    
    print(f"已匹配分配: {len(matched_allocated)} 人")
    print(f"随机分配: {len(random_allocated)} 人")
    
    # 3. 分析匹配差异
    print(f"\n🔍 分析匹配差异")
    print("-" * 50)
    
    # 创建姓名到分配结果的映射
    name_to_allocation = {}
    for result in allocation_result:
        name = result.get('姓名', '').strip()
        if name:
            name_to_allocation[name] = result
    
    # 检查每个已匹配组的分配情况
    print("检查已匹配组的分配情况:")
    
    perfect_groups = 0  # 完美分配的组
    partial_groups = 0  # 部分分配的组
    scattered_groups = 0  # 分散分配的组
    missing_groups = 0  # 未分配的组
    
    for group_key, group_students in matched_groups.items():
        print(f"\n组 {group_key} ({len(group_students)}人):")
        
        # 检查组内学生的分配情况
        allocated_rooms = set()
        allocated_students = []
        missing_students = []
        
        for student_data in group_students:
            name = student_data.get('姓名', '').strip()
            print(f"  学生: {name}")
            
            if name in name_to_allocation:
                allocation = name_to_allocation[name]
                room_key = f"{allocation['宿舍楼']}_{allocation['房间号']}"
                allocated_rooms.add(room_key)
                allocated_students.append((name, allocation))
                print(f"    ✅ 已分配: {allocation['宿舍楼']} {allocation['房间号']} 床位{allocation['床位号']} ({allocation['分配类型']})")
            else:
                missing_students.append(name)
                print(f"    ❌ 未分配")
        
        # 分析组的分配质量
        if len(missing_students) == 0:
            if len(allocated_rooms) == 1:
                perfect_groups += 1
                print(f"  🎯 完美分配: 所有学生都在同一房间 {list(allocated_rooms)[0]}")
            elif len(allocated_rooms) > 1:
                scattered_groups += 1
                print(f"  ⚠️  分散分配: 学生分布在 {len(allocated_rooms)} 个不同房间: {allocated_rooms}")
            else:
                partial_groups += 1
                print(f"  ⚠️  部分分配: 有学生未分配")
        else:
            missing_groups += 1
            print(f"  ❌ 有学生未分配: {missing_students}")
    
    # 4. 统计分析结果
    print(f"\n📊 分配质量统计")
    print("-" * 50)
    
    total_groups = len(matched_groups)
    print(f"总组数: {total_groups}")
    print(f"完美分配组: {perfect_groups} ({perfect_groups/total_groups*100:.1f}%)")
    print(f"分散分配组: {scattered_groups} ({scattered_groups/total_groups*100:.1f}%)")
    print(f"部分分配组: {partial_groups} ({partial_groups/total_groups*100:.1f}%)")
    print(f"未分配组: {missing_groups} ({missing_groups/total_groups*100:.1f}%)")
    
    # 5. 分析分散分配的原因
    print(f"\n🔍 分析分散分配原因")
    print("-" * 50)
    
    if scattered_groups > 0:
        print("分散分配的组详情:")
        for group_key, group_students in matched_groups.items():
            allocated_rooms = set()
            for student_data in group_students:
                name = student_data.get('姓名', '').strip()
                if name in name_to_allocation:
                    allocation = name_to_allocation[name]
                    room_key = f"{allocation['宿舍楼']}_{allocation['房间号']}"
                    allocated_rooms.add(room_key)
            
            if len(allocated_rooms) > 1:
                print(f"\n组 {group_key}:")
                print(f"  学生数: {len(group_students)}")
                print(f"  分布房间: {allocated_rooms}")
                
                # 分析房间容量
                for room in allocated_rooms:
                    room_students = []
                    for student_data in group_students:
                        name = student_data.get('姓名', '').strip()
                        if name in name_to_allocation:
                            allocation = name_to_allocation[name]
                            if f"{allocation['宿舍楼']}_{allocation['房间号']}" == room:
                                room_students.append(name)
                    print(f"    {room}: {len(room_students)}人 - {room_students}")
    
    # 6. 检查床位容量问题
    print(f"\n🏠 检查床位容量问题")
    print("-" * 50)
    
    # 统计每个房间的床位数
    room_bed_count = defaultdict(int)
    with open("宿舍情况_utf8.csv", 'r', encoding='utf-8-sig', newline='') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row.get('床位号', '').strip():
                room_key = f"{row['宿舍楼']}_{row['房间号']}"
                room_bed_count[room_key] += 1
    
    print("房间床位容量统计 (前10个):")
    for i, (room, bed_count) in enumerate(sorted(room_bed_count.items())[:10]):
        print(f"  {room}: {bed_count}个床位")
    
    # 检查4人组是否能找到4人间
    four_person_rooms = [room for room, count in room_bed_count.items() if count >= 4]
    print(f"\n4人及以上房间数: {len(four_person_rooms)}")
    print(f"需要4人间的组数: {sum(1 for group in matched_groups.values() if len(group) >= 4)}")
    
    return {
        'total_groups': total_groups,
        'perfect_groups': perfect_groups,
        'scattered_groups': scattered_groups,
        'partial_groups': partial_groups,
        'missing_groups': missing_groups
    }

if __name__ == "__main__":
    analyze_matching_issues()
